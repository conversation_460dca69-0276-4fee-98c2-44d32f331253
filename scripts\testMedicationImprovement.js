/**
 * Script para testar a função de melhoria de medicamentos
 * 
 * Como usar:
 * 1. Configure OPENAI_API_KEY no .env
 * 2. Execute: node scripts/testMedicationImprovement.js
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Carregar variáveis de ambiente
dotenv.config()

const SUPABASE_URL = "https://bxedpdmgvgatjdfxgxij.supabase.co"
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4ZWRwZG1ndmdhdGpkZnhneGlqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzIyNzk3MTgsImV4cCI6MjA0Nzg1NTcxOH0.cjoaggOXt1kY9WmVNbAipCOQ2dP4PWLP43KMf8cO8Wo"

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

// Exemplos de medicamentos para testar
const testMedications = [
  {
    name: "17-valerato de betametasona + sulfato de neomicina",
    description: "Corticosteroide tópico com antibiótico"
  },
  {
    name: "paracetamol + codeína",
    description: "Analgésico opioide"
  },
  {
    name: "ibuprofeno",
    description: "Anti-inflamatório não esteroidal"
  },
  {
    name: "amoxicilina + ácido clavulânico",
    description: "Antibiótico de amplo espectro"
  },
  {
    name: "dipirona sódica",
    description: "Analgésico e antitérmico"
  }
]

async function testMedicationImprovement() {
  console.log('🧪 Iniciando teste da função de melhoria de medicamentos...\n')

  for (let i = 0; i < testMedications.length; i++) {
    const medication = testMedications[i]
    
    console.log(`📋 Teste ${i + 1}/${testMedications.length}`)
    console.log(`   Medicamento: ${medication.name}`)
    console.log(`   Descrição: ${medication.description}`)
    
    try {
      const { data, error } = await supabase.functions.invoke('improve-medication', {
        body: {
          active_principles: medication.name,
          current_data: {
            description: medication.description
          }
        }
      })

      if (error) {
        console.log(`   ❌ Erro: ${error.message}`)
        continue
      }

      if (!data.success) {
        console.log(`   ❌ Falha: ${data.error}`)
        continue
      }

      const result = data.data
      console.log(`   ✅ Nome melhorado: ${result.improved_name}`)
      console.log(`   🏷️  Classe: ${result.therapeutic_class}`)
      console.log(`   📊 Número da classe: ${result.therapeutic_class_number}`)
      
    } catch (error) {
      console.log(`   ❌ Erro inesperado: ${error.message}`)
    }
    
    console.log('') // Linha em branco
    
    // Pausa entre requisições para não sobrecarregar
    if (i < testMedications.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
  }

  console.log('🎉 Teste concluído!')
}

async function testLocalFunction() {
  console.log('🔧 Testando função localmente (simulação)...\n')
  
  // Simular resposta da OpenAI para teste local
  const mockOpenAIResponse = {
    improved_name: "17-Valerato de Betametasona + Sulfato de Neomicina",
    therapeutic_class: "5. Corticosteroides / Anti-inflamatórios — Tópicos",
    therapeutic_class_number: 5
  }
  
  console.log('📋 Teste local com dados simulados:')
  console.log(`   Entrada: "17-valerato de betametasona + sulfato de neomicina"`)
  console.log(`   ✅ Nome melhorado: ${mockOpenAIResponse.improved_name}`)
  console.log(`   🏷️  Classe: ${mockOpenAIResponse.therapeutic_class}`)
  console.log(`   📊 Número da classe: ${mockOpenAIResponse.therapeutic_class_number}`)
  console.log('')
}

async function checkSupabaseConnection() {
  console.log('🔗 Verificando conexão com Supabase...')
  
  try {
    const { data, error } = await supabase
      .from('active_ingredients')
      .select('count')
      .limit(1)
    
    if (error) {
      console.log(`   ❌ Erro de conexão: ${error.message}`)
      return false
    }
    
    console.log('   ✅ Conexão com Supabase OK')
    return true
  } catch (error) {
    console.log(`   ❌ Erro inesperado: ${error.message}`)
    return false
  }
}

async function main() {
  console.log('🚀 Script de Teste - Melhoria de Medicamentos com IA')
  console.log('=' .repeat(60))
  console.log('')
  
  // Verificar conexão
  const isConnected = await checkSupabaseConnection()
  console.log('')
  
  if (!isConnected) {
    console.log('❌ Não foi possível conectar ao Supabase. Executando teste local...')
    await testLocalFunction()
    return
  }
  
  // Verificar se a função está deployada
  console.log('🔍 Verificando se a função edge está disponível...')
  
  try {
    const { data, error } = await supabase.functions.invoke('improve-medication', {
      body: {
        active_principles: "teste",
        current_data: {}
      }
    })
    
    // Se chegou até aqui, a função existe (mesmo que retorne erro)
    console.log('   ✅ Função edge encontrada')
    console.log('')
    
    // Executar testes reais
    await testMedicationImprovement()
    
  } catch (error) {
    if (error.message.includes('Function not found')) {
      console.log('   ❌ Função edge não encontrada. Você precisa fazer o deploy primeiro.')
      console.log('   💡 Execute: supabase functions deploy improve-medication')
      console.log('')
      console.log('   Executando teste local como alternativa...')
      await testLocalFunction()
    } else {
      console.log(`   ⚠️  Erro ao verificar função: ${error.message}`)
      console.log('   Tentando executar testes mesmo assim...')
      console.log('')
      await testMedicationImprovement()
    }
  }
}

// Executar o script
main().catch(console.error)
