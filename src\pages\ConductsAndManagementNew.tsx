import React, { useState } from "react";
import { motion } from "framer-motion";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ChevronLeft, Search, Loader2, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from 'react-router-dom';
import { Input } from "@/components/ui/input";
import { CategoryCard } from "@/components/conducts/CategoryCard";
import { performFuzzySearch } from "@/components/search/utils/fuzzySearch";
import { perfLogger } from "@/utils/performanceLogger";

interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon_url?: string;
  coming_soon?: boolean;
  display_order?: number;
  summary_count?: number;
  topics?: Topic[];
}

interface Topic {
  id: string;
  name: string;
  slug: string;
  category_id: string;
}

// 🔧 SOLUÇÃO RADICAL: Hooks diretos no componente

const ConductsAndManagementNew = React.memo(() => {
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();

  // 🔧 SOLUÇÃO RADICAL: React Query direto no componente
  const { data: categories = [], isLoading: isLoadingCategories } = useQuery({
    queryKey: ['conducts-categories-public'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_conducts_categories')
        .select('*')
        .order('display_order', { ascending: true });
      if (error) throw error;
      return data as Category[];
    },
    staleTime: 5 * 60 * 1000,
  });

  const { data: topics = [], isLoading: isLoadingTopics } = useQuery({
    queryKey: ['conducts-topics-public'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_conducts_topics')
        .select('id, name, slug, category_id');
      if (error) throw error;
      return data as Topic[];
    },
    staleTime: 5 * 60 * 1000,
  });

  const { data: summaries = [], isLoading: isLoadingSummaries } = useQuery({
    queryKey: ['conducts-summaries-public'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_conducts_summaries')
        .select('topic_id')
        .eq('published', true);
      if (error) throw error;
      return data;
    },
    staleTime: 5 * 60 * 1000,
  });

  // 🔧 CORREÇÃO: Buscar também resumos otimizados
  const { data: optimizedSummaries = [], isLoading: isLoadingOptimized } = useQuery({
    queryKey: ['conducts-optimized-public'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_conducts_optimized')
        .select('topic_id')
        .eq('published', true);
      if (error) throw error;
      return data;
    },
    staleTime: 5 * 60 * 1000,
  });

  const loading = isLoadingCategories || isLoadingTopics || isLoadingSummaries || isLoadingOptimized;

  // Processamento simples
  const enrichedCategories = React.useMemo(() => {
    if (loading || !categories.length) return [];

    // 🔧 CORREÇÃO: Contar TODOS os resumos (normais + otimizados) por tópico
    const topicCounts: Record<string, number> = {};

    // Contar resumos normais
    summaries.forEach((summary: any) => {
      if (summary.topic_id) {
        topicCounts[summary.topic_id] = (topicCounts[summary.topic_id] || 0) + 1;
      }
    });

    // Contar resumos otimizados
    optimizedSummaries.forEach((summary: any) => {
      if (summary.topic_id) {
        topicCounts[summary.topic_id] = (topicCounts[summary.topic_id] || 0) + 1;
      }
    });

    // Contar summaries por categoria
    const categoryCounts: Record<string, number> = {};
    topics.forEach(topic => {
      const count = topicCounts[topic.id] || 0;
      if (count > 0) {
        categoryCounts[topic.category_id] = (categoryCounts[topic.category_id] || 0) + count;
      }
    });

    // Enriquecer categorias e ordenar por conteúdo disponível
    const enriched = categories.map(category => ({
      ...category,
      summary_count: categoryCounts[category.id] || 0,
      topics: topics.filter(topic => topic.category_id === category.id)
    }));

    // 🔧 CORREÇÃO: Ordenar por conteúdo disponível (coming_soon por último)
    return enriched.sort((a, b) => {
      if (a.coming_soon && !b.coming_soon) return 1;
      if (!a.coming_soon && b.coming_soon) return -1;
      return (b.summary_count || 0) - (a.summary_count || 0);
    });
  }, [categories, topics, summaries, optimizedSummaries, loading]);

  // Filtrar categorias
  const { filteredCategories, fuzzyResults } = React.useMemo(() => {

    if (!searchTerm.trim()) {
      return {
        filteredCategories: enrichedCategories,
        fuzzyResults: []
      };
    }

    // Busca normal
    const filtered = enrichedCategories.filter(category => {
      const nameMatch = category.name.toLowerCase().includes(searchTerm.toLowerCase());
      const descriptionMatch = category.description?.toLowerCase().includes(searchTerm.toLowerCase());
      const topicMatch = category.topics?.some(topic =>
        topic.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      return nameMatch || descriptionMatch || topicMatch;
    });

    // Busca fuzzy se poucos resultados
    let fuzzyResults: Category[] = [];
    if (filtered.length < 3 && searchTerm.length >= 3) {
      const fuzzySearchResults = performFuzzySearch(
        enrichedCategories,
        searchTerm,
        ['name', 'description'],
        5,
        0.5
      );

      fuzzyResults = fuzzySearchResults
        .filter(result => !filtered.some(cat => cat.id === result.item.id))
        .map(result => ({
          ...result.item,
          isFuzzyMatch: true
        }));
    }

    return {
      filteredCategories: filtered,
      fuzzyResults
    };
  }, [searchTerm, enrichedCategories]);

  // Performance monitoring desabilitado para teste
  // perfLogger.logRender('ConductsAndManagement', { searchTerm, categoriesCount: filteredCategories.length });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex flex-col">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-6 md:py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
            <div className="flex items-center gap-2 md:gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/')}
                className="flex items-center gap-1 md:gap-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                <ChevronLeft className="w-4 h-4" />
                <span className="text-sm md:text-base">Início</span>
              </Button>
              
              <h1 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
                Condutas e Manejos
              </h1>
            </div>

            <div className="flex items-center gap-2">
              <Zap className="w-5 h-5 text-yellow-500" />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {enrichedCategories.length} categorias
              </span>
            </div>
          </div>

          {/* Busca */}
          <div className="relative mb-6">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 md:w-5 md:h-5" />
            <Input
              type="text"
              placeholder="Buscar categorias ou tópicos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 md:pl-12 text-sm md:text-base"
            />
          </div>

          {/* Loading */}
          {loading && (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
            </div>
          )}

          {/* Categorias */}
          {!loading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
              {filteredCategories.map((category) => (
                <motion.div
                  key={category.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <CategoryCard category={category} index={0} />
                </motion.div>
              ))}

              {/* Resultados fuzzy */}
              {fuzzyResults.map((category) => (
                <motion.div
                  key={`fuzzy-${category.id}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <CategoryCard category={category} index={0} />
                </motion.div>
              ))}
            </div>
          )}

          {/* Sem resultados */}
          {!loading && filteredCategories.length === 0 && fuzzyResults.length === 0 && (
            <div className="text-center py-8 md:py-12">
              <h3 className="text-lg md:text-xl lg:text-2xl font-medium text-gray-600 dark:text-gray-300">
                Nenhuma categoria encontrada
              </h3>
              <p className="text-sm md:text-base lg:text-lg text-gray-500 dark:text-gray-400 mt-2">
                Tente ajustar sua pesquisa ou explore outras opções.
              </p>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
});

ConductsAndManagementNew.displayName = 'ConductsAndManagementNew';

export default ConductsAndManagementNew;
