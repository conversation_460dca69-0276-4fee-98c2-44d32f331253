// Sistema de logs de performance para identificar problemas
interface PerformanceLog {
  component: string;
  action: string;
  timestamp: number;
  data?: any;
  duration?: number;
}

class PerformanceLogger {
  private logs: PerformanceLog[] = [];
  private timers: Map<string, number> = new Map();

  // Log de renderização de componente (DESABILITADO)
  logRender(component: string, data?: any) {
    // Logs desabilitados para produção
    return;
  }

  // Log de requisição HTTP (DESABILITADO)
  logRequest(component: string, url: string, data?: any) {
    // Logs desabilitados para produção
    return;
  }

  // Log de useEffect (DESABILITADO)
  logEffect(component: string, dependencies: any[], action: string = 'EFFECT') {
    // Logs desabilitados para produção
    return;
  }

  // Iniciar timer para medir duração (DESABILITADO)
  startTimer(key: string) {
    // Logs desabilitados para produção
    return;
  }

  // Finalizar timer e log da duração (DESABILITADO)
  endTimer(key: string, component: string, action: string = 'TIMER') {
    // Logs desabilitados para produção
    return;
  }

  // Obter estatísticas de performance (DESABILITADO)
  getStats() {
    // Logs desabilitados para produção
    return {};
  }

  private findDuplicateRequests(logs: PerformanceLog[]) {
    return [];
  }

  private findExcessiveRenders(logs: PerformanceLog[]) {
    return [];
  }

  // Limpar logs antigos (DESABILITADO)
  cleanup() {
    // Logs desabilitados para produção
    return;
  }
}

// Instância global do logger
export const perfLogger = new PerformanceLogger();

// Limpar logs a cada 5 minutos
setInterval(() => perfLogger.cleanup(), 300000);

// Expor função global para debug no console
(window as any).showPerfStats = () => {
  console.log('📊 PERFORMANCE STATISTICS:');
  return perfLogger.getStats();
};

// Expor função para limpar logs
(window as any).clearPerfLogs = () => {
  perfLogger.cleanup();
  console.log('🧹 Performance logs cleared');
};

// Expor função para limpar todos os caches
(window as any).clearAllCaches = () => {
  // Limpar caches dos componentes (se existirem)
  if (typeof window !== 'undefined') {
    // Tentar acessar os caches globalmente
    const caches = ['requestCache', 'summaryCache', 'topicListCache'];
    caches.forEach(cacheName => {
      try {
        const cache = (window as any)[cacheName];
        if (cache && typeof cache.clear === 'function') {
          cache.clear();
          console.log(`🧹 ${cacheName} cleared`);
        }
      } catch (e) {
        // Cache não existe ou não é acessível
      }
    });
  }
  console.log('🧹 All caches cleared');
};
