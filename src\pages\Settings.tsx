
import { useState, useEffect } from "react";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { useSession } from "@supabase/auth-helpers-react";
import { supabase } from "@/integrations/supabase/client";
import { useSuccessDialog, useErrorDialog } from "@/hooks/use-feedback-dialog";
import { ChevronLeft } from "lucide-react";
import { Link } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ProfileForm from "@/components/settings/ProfileForm";
import AvatarUpload from "@/components/settings/AvatarUpload";
import DeleteAccountForm from "@/components/settings/DeleteAccountForm";
import { getThemeClasses } from "@/components/ui/theme-utils";
import { SiteFeedbackDialog } from "@/components/feedback/SiteFeedbackDialog";
import { FeedbackPage } from "@/pages/feedback/FeedbackPage";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

const Settings = () => {
  const session = useSession();
  const showSuccessDialog = useSuccessDialog();
  const showErrorDialog = useErrorDialog();
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);

  useEffect(() => {
    if (session?.user) {
      fetchProfile();
    } else {
      // Se não há usuário, limpar o estado
      setProfile(null);
      setLoading(false);
    }
  }, [session]);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("secure_profiles")
        .select("*")
        .eq("id", session?.user?.id)
        .single();

      if (error) {
        // Se o erro for que não encontrou o perfil, tentar criar um
        if (error.code === 'PGRST116') {
          console.log("Profile not found, creating new profile...");
          await createProfile();
          return;
        }
        throw error;
      }

      // Verificar se recebemos uma mensagem de segurança
      if (data.security_message) {
        console.error("Security message:", data.security_message);
        showErrorDialog(
          "Acesso negado",
          data.security_message
        );
        return;
      }

      setProfile(data);
    } catch (error) {
      console.error("Error fetching profile:", error);
      showErrorDialog(
        "Erro ao carregar perfil",
        "Não foi possível carregar suas informações."
      );
    } finally {
      setLoading(false);
    }
  };

  const createProfile = async () => {
    try {
      console.log("Creating profile for user:", session?.user?.id);

      // Usar upsert para criar ou atualizar o perfil
      const { error: profileError } = await supabase
        .from("profiles")
        .upsert({
          id: session?.user?.id,
          username: session?.user?.email || "",
          full_name: session?.user?.user_metadata?.full_name || "Usuário",
          formation_area: session?.user?.user_metadata?.formation_area || "Não informado",
          graduation_year: session?.user?.user_metadata?.graduation_year || "Não informado",
          is_student: session?.user?.user_metadata?.is_student || false,
          is_professional: session?.user?.user_metadata?.is_professional || false,
          avatar_url: session?.user?.user_metadata?.avatar_url || null,
          professional_email: "",
          phone: "",
          registration_number: ""
        }, {
          onConflict: 'id'
        });

      if (profileError) {
        console.error("Error upserting profile:", profileError);
        throw profileError;
      }

      // Aguardar um pouco para garantir que o perfil foi criado
      await new Promise(resolve => setTimeout(resolve, 500));

      // Agora tentar buscar novamente
      const { data, error } = await supabase
        .from("secure_profiles")
        .select("*")
        .eq("id", session?.user?.id)
        .single();

      if (error) {
        console.error("Error fetching profile after creation:", error);
        throw error;
      }

      setProfile(data);
      console.log("Profile created and loaded successfully");
    } catch (error) {
      console.error("Error creating profile:", error);
      showErrorDialog(
        "Erro ao criar perfil",
        "Não foi possível criar seu perfil. Tente novamente ou entre em contato com o suporte."
      );
    }
  };

  const convertToWebP = async (file: File): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Failed to get canvas context'));
          return;
        }
        ctx.drawImage(img, 0, 0);
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to convert image to WebP'));
          }
        }, 'image/webp', 0.8);
      };
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  };

  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      setUploading(true);
      if (!event.target.files || event.target.files.length === 0) {
        throw new Error("Você precisa selecionar uma imagem para fazer upload.");
      }

      const file = event.target.files[0];
      const webpBlob = await convertToWebP(file);
      const webpFile = new File([webpBlob], `${file.name.split('.')[0]}.webp`, { type: 'image/webp' });

      const filePath = `${session?.user?.id}-${Math.random()}.webp`;

      const { error: uploadError } = await supabase.storage
        .from("avatars")
        .upload(filePath, webpFile);

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from("avatars")
        .getPublicUrl(filePath);

      const { error: updateError } = await supabase
        .from("profiles")
        .update({ avatar_url: publicUrl })
        .eq("id", session?.user?.id);

      if (updateError) throw updateError;

      setProfile({ ...profile, avatar_url: publicUrl });
      showSuccessDialog(
        "Avatar atualizado",
        "Sua foto de perfil foi atualizada com sucesso."
      );
    } catch (error: any) {
      console.error("Error uploading avatar:", error);
      showErrorDialog(
        "Erro ao fazer upload",
        "Não foi possível atualizar sua foto de perfil."
      );
    } finally {
      setUploading(false);
    }
  };

  const handleProfileUpdate = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      const updateData = {
        full_name: profile.full_name,
        formation_area: profile.formation_area,
        graduation_year: profile.graduation_year,
        is_student: profile.is_student,
        is_professional: profile.is_professional,
        professional_email: profile.professional_email,
        phone: profile.phone,
        registration_number: profile.registration_number,
      };

      const { error } = await supabase
        .from("profiles")
        .update(updateData)
        .eq("id", session?.user?.id);

      if (error) throw error;

      showSuccessDialog(
        "Perfil atualizado",
        "Suas informações foram atualizadas com sucesso."
      );

      await fetchProfile();
    } catch (error: any) {
      console.error("Error updating profile:", error);
      showErrorDialog(
        "Erro ao atualizar perfil",
        "Não foi possível atualizar suas informações."
      );
    }
  };

  if (loading) {
    return (
      <div className={getThemeClasses.pageBackground()}>
        <Header />
        <div className="container mx-auto px-4 py-8 text-center dark:text-gray-300">Carregando...</div>
        <Footer />
      </div>
    );
  }

  return (
    <div className={getThemeClasses.pageBackground()}>
      <HelmetWrapper>
        <title>PedBook | Configurações</title>
        <meta name="description" content="Gerencie suas configurações e perfil no PedBook." />
      </HelmetWrapper>

      <Header />
      <main className="container mx-auto px-2 sm:px-4 py-4 sm:py-8 relative space-y-4 sm:space-y-6">
        {/* Header simples e limpo */}
        <div className="flex items-center justify-between mb-6">
          <Link to="/" className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors">
            <ChevronLeft className="h-5 w-5" />
            <span className="text-sm sm:text-base">Voltar</span>
          </Link>
          <div className="text-center flex-1 mx-4">
            <h1 className="text-lg sm:text-xl font-bold">
              Configurações
            </h1>
            <p className="text-xs sm:text-sm text-muted-foreground mt-1">
              Gerencie suas informações
            </p>
          </div>
          <div className="w-16"></div> {/* Spacer para centralizar */}
        </div>

        {/* BOTÃO DE TESTE SENTRY - REMOVER DEPOIS */}
        <div className="w-full max-w-2xl mx-auto mb-4 space-y-2">
          <Button
            onClick={() => {throw new Error("🧪 Teste do Sentry - Erro intencional!");}}
            variant="destructive"
            size="sm"
            className="w-full"
          >
            🧪 Testar Sentry (Clique para gerar erro)
          </Button>

          <Button
            onClick={() => {throw new Error("This is your first error!");}}
            variant="outline"
            size="sm"
            className="w-full border-red-200 text-red-600 hover:bg-red-50"
          >
            Break the world (Teste oficial do Sentry)
          </Button>
        </div>

        {/* Card principal limpo */}
        <Card className="w-full max-w-2xl mx-auto">
          <CardHeader className="text-center px-4 sm:px-6 py-6">
            <AvatarUpload
              profile={profile}
              session={session}
              uploading={uploading}
              handleAvatarUpload={handleAvatarUpload}
            />
            <div className="mt-4">
              <h2 className="text-lg sm:text-xl font-semibold">
                {profile?.full_name || "Usuário"}
              </h2>
              <p className="text-sm text-muted-foreground">
                {profile?.formation_area || "Área não informada"}
              </p>
            </div>
          </CardHeader>
          <CardContent className="space-y-6 sm:space-y-8 px-4 sm:px-6 pb-6 sm:pb-8">
            <ProfileForm
              profile={profile}
              setProfile={setProfile}
              handleProfileUpdate={handleProfileUpdate}
            />
          </CardContent>
        </Card>

        <div className="w-full max-w-2xl mx-auto">
          <DeleteAccountForm />
        </div>
      </main>
      <Footer />

      {/* Only render one dialog - moved to the feedback page */}
      <Dialog
        open={showFeedbackDialog}
        onOpenChange={(value) => {
          setShowFeedbackDialog(value);
          // Ensure we don't have any stale state that could cause issues
          if (!value) {
            document.body.style.pointerEvents = 'auto';
          }
        }}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto dark:bg-slate-800">
          <DialogHeader>
            <DialogTitle>Feedback</DialogTitle>
          </DialogHeader>
          <FeedbackPage />
        </DialogContent>
      </Dialog>

      {/* This SiteFeedbackDialog will render itself conditionally based on timer */}
    </div>
  );
};

export default Settings;
