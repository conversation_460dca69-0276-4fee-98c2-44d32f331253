import React, { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ImageWithCaption, parseImageWithCaption } from "@/components/ui/ImageWithCaption";
import { ImageGallery } from "@/components/ui/ImageGallery";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { List, ChevronRight } from "lucide-react";

interface OptimizedConductsViewProps {
  title: string;
  conductsContent: string;
  treatmentContent: string;
  hasTreatment: boolean;
}

export const OptimizedConductsView: React.FC<OptimizedConductsViewProps> = ({
  title,
  conductsContent,
  treatmentContent,
  hasTreatment
}) => {
  const [activeTab, setActiveTab] = useState("conducts");
  const [isMobile, setIsMobile] = useState(false);
  const [activeSection, setActiveSection] = useState<string>("");
  const [activeSectionTitle, setActiveSectionTitle] = useState<string>("");
  const [summaryDialogOpen, setSummaryDialogOpen] = useState(false);

  // Função para trocar aba e ir ao topo
  const handleTabChange = useCallback((newTab: string) => {
    setActiveTab(newTab);

    // 🎯 Notificar ConductsSummary sobre mudança de aba
    const event = new CustomEvent('optimizedTabChange', { detail: newTab });
    window.dispatchEvent(event);

    // Resetar seção ativa ao trocar de aba
    setActiveSection("");
    setActiveSectionTitle("");

    // Scroll para o topo do conteúdo
    const contentArea = document.querySelector('.content-scroll-area');
    if (contentArea) {
      contentArea.scrollTop = 0;
    }
  }, []);

  // Função para navegar para uma seção específica
  const navigateToSection = useCallback((sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      // Fechar o dialog primeiro
      setSummaryDialogOpen(false);

      // Aguardar um pouco para o dialog fechar completamente
      setTimeout(() => {
        // Scroll com offset para não esconder o header
        const contentArea = document.querySelector('.content-scroll-area');
        if (contentArea) {
          const elementTop = element.offsetTop;
          // Offset para considerar o header e "Lendo agora"
          const offset = isMobile ? 100 : 80;
          contentArea.scrollTo({
            top: elementTop - offset,
            behavior: 'smooth'
          });
        } else {
          // Fallback para scrollIntoView
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 150);
    }
  }, [isMobile]);



  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const extractSectionsFromMarkdown = useCallback((content: string) => {
    if (!content) return [];

    const lines = content.split('\n');
    const sections: any[] = [];

    lines.forEach((line) => {
      if (line.startsWith('## ')) {
        // Seção principal
        const title = line.replace('## ', '').trim();
        const id = title.toLowerCase()
          .replace(/[^\w\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim();

        sections.push({
          id,
          title,
          level: 2
        });
      }
      // Removido: subseções (### ) não são mais incluídas no menu lateral
    });

    return sections;
  }, []);

  const sections = useMemo(() => {
    const currentContent = activeTab === "conducts" ? conductsContent : treatmentContent;
    const extractedSections = extractSectionsFromMarkdown(currentContent);



    return extractedSections;
  }, [activeTab, conductsContent, treatmentContent, extractSectionsFromMarkdown]);

  // Detectar seção ativa durante scroll (apenas para abas de conteúdo)
  useEffect(() => {
    // Só ativar scroll tracking para abas de conteúdo
    if (activeTab === 'images') return;

    let timeoutId: NodeJS.Timeout;
    let lastActiveSection = activeSection; // Capturar o valor atual
    let lastScrollPosition = 0;

    const handleScroll = () => {
      // Throttle para evitar execuções excessivas
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
      const contentArea = document.querySelector('.content-scroll-area');
      if (!contentArea) return;

      const scrollPosition = contentArea.scrollTop + 20; // Reduzido para 20px para ativação mais rápida
      let currentSection = '';



      // Verificar seções principais primeiro (ordem reversa para pegar a mais próxima)
      const mainSections = sections.filter(s => s.level === 2);


      // Nova lógica com hysteresis para evitar oscilações
      const ACTIVATION_OFFSET = 300; // Ativa 300px antes da seção (reduzido para mais responsividade)
      const STABILITY_ZONE = 30;     // Zona de estabilidade reduzida para 30px

      // Encontrar todas as seções candidatas
      const candidates = [];

      for (const section of mainSections) {
        const element = document.getElementById(section.id);

        if (element) {
          const elementTop = element.offsetTop;
          const distanceToSection = elementTop - scrollPosition;

          // Candidata se está próxima ou já passou
          if (distanceToSection <= ACTIVATION_OFFSET) {
            candidates.push({
              id: section.id,
              title: section.title,
              elementTop,
              distanceToSection,
              isPassed: scrollPosition >= elementTop
            });
          }
        }
      }



      // Se há seção ativa atual, verificar se deve manter (hysteresis)
      if (lastActiveSection) {
        const currentActiveCandidate = candidates.find(c => c.id === lastActiveSection);
        if (currentActiveCandidate) {
          // Verificar se há uma seção muito mais próxima
          const closerSections = candidates.filter(c =>
            c.id !== lastActiveSection &&
            Math.abs(c.distanceToSection) < Math.abs(currentActiveCandidate.distanceToSection) - 30
          );

          // Manter seção atual apenas se não há seção muito mais próxima
          const shouldKeepCurrent = currentActiveCandidate.distanceToSection >= -STABILITY_ZONE && closerSections.length === 0;
          if (shouldKeepCurrent) {
            currentSection = lastActiveSection;

          }
        }
      }

      // Se não manteve a atual, escolher a melhor candidata
      if (!currentSection && candidates.length > 0) {
        // Preferir seções que já passaram, senão a mais próxima
        const passedSections = candidates.filter(c => c.isPassed);
        const bestCandidate = passedSections.length > 0
          ? passedSections[passedSections.length - 1] // Última que passou
          : candidates[candidates.length - 1]; // Mais próxima

        currentSection = bestCandidate.id;

      }

      // Se ainda não há seção e há candidatas próximas, forçar ativação
      if (!currentSection && candidates.length > 0) {
        const closestCandidate = candidates.reduce((closest, current) =>
          Math.abs(current.distanceToSection) < Math.abs(closest.distanceToSection) ? current : closest
        );

        if (Math.abs(closestCandidate.distanceToSection) <= 100) {
          currentSection = closestCandidate.id;

        }
      }

      // Verificar subseções APENAS se não encontrou seção principal ativa
      if (!currentSection) {
        const subSections = sections.filter(s => s.level === 3);

        for (let i = subSections.length - 1; i >= 0; i--) {
          const section = subSections[i];
          const h3Elements = contentArea.querySelectorAll('h3');

          for (const h3Element of h3Elements) {
            const h3Text = h3Element.textContent?.trim() || '';
            const cleanH3Text = h3Text.replace(/\*\*/g, '').replace(/\*/g, '').trim();

            if (cleanH3Text === section.title) {
              const elementTop = h3Element.offsetTop;

              if (scrollPosition >= elementTop - 30) {
                currentSection = section.id;
    
                break;
              }
            }
          }
          if (currentSection === section.id) break;
        }
      }



      if (currentSection !== lastActiveSection) {
        // Atualizar variável local e estados
        lastActiveSection = currentSection;
        setActiveSection(currentSection);

        // Encontrar o título da seção ativa
        const activeSectionData = sections.find(s => s.id === currentSection);
        if (activeSectionData) {
          setActiveSectionTitle(activeSectionData.title);
        } else {
          setActiveSectionTitle('');
        }
      }
      }, 16); // Throttle de 16ms para 60fps - mais responsivo
    };

    const contentArea = document.querySelector('.content-scroll-area');
    if (contentArea) {
      contentArea.addEventListener('scroll', handleScroll);
      handleScroll();
    }

    return () => {
      clearTimeout(timeoutId);
      if (contentArea) {
        contentArea.removeEventListener('scroll', handleScroll);
      }
    };
  }, [sections, activeTab]); // Incluir activeTab para reativar quando trocar de aba





  const scrollToSection = useCallback((sectionId: string, closeAccordion = false) => {
    const section = sections.find(s => s.id === sectionId);
    if (!section) return;

    // Fechar accordion imediatamente se solicitado
    if (closeAccordion) {
      const accordionTrigger = document.querySelector('[data-state="open"]');
      if (accordionTrigger) {
        (accordionTrigger as HTMLElement).click();
      }
    }

    if (section.level === 2) {
      const element = document.getElementById(sectionId);
      if (element) {
        const contentArea = document.querySelector('.content-scroll-area');
        if (contentArea) {
          // Adicionar classe de loading temporária
          const button = document.querySelector(`[data-section-id="${sectionId}"]`);
          if (button) {
            button.classList.add('animate-pulse');
            setTimeout(() => button.classList.remove('animate-pulse'), 800);
          }

          const targetPosition = element.offsetTop - 80;

          // Scroll suave customizado com easing
          const startPosition = contentArea.scrollTop;
          const distance = targetPosition - startPosition;
          const duration = Math.min(800, Math.max(400, Math.abs(distance) * 0.5)); // Duração baseada na distância
          const startTime = performance.now();

          const easeInOutCubic = (t: number) => {
            return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
          };

          const animateScroll = (currentTime: number) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const easedProgress = easeInOutCubic(progress);

            const currentPosition = startPosition + (distance * easedProgress);
            contentArea.scrollTop = currentPosition;

            if (progress < 1) {
              requestAnimationFrame(animateScroll);
            }
          };

          requestAnimationFrame(animateScroll);
        }
      }
    }
  }, [sections]);

  const markdownComponents = useMemo(() => ({
    table: ({ children }: any) => (
      <div className="overflow-x-auto my-4">
        <table className="min-w-full border-collapse border border-gray-300 dark:border-gray-600">
          {children}
        </table>
      </div>
    ),
    th: ({ children }: any) => (
      <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 bg-gray-50 dark:bg-gray-800 font-semibold text-left text-gray-900 dark:text-gray-100">
        {children}
      </th>
    ),
    td: ({ children }: any) => (
      <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-gray-700 dark:text-gray-300">
        {children}
      </td>
    ),
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-blue-500 dark:border-blue-400 pl-4 my-4 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-r-lg text-gray-700 dark:text-gray-300">
        {children}
      </blockquote>
    ),
    h2: ({ children }: any) => {
      const text = typeof children === 'string' ? children : children?.toString() || '';
      const id = text.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();

      return (
        <div className="first:mt-0 mt-8 mb-8">
          <h2 id={id} className="relative text-xl font-bold text-gray-900 dark:text-gray-100 scroll-mt-20 pb-4">
            <span className="relative z-10 bg-white dark:bg-gray-900 pr-4">
              {children}
            </span>
            {/* Linha decorativa suave com gradiente */}
            <div className="absolute left-0 bottom-0 w-full h-px bg-gradient-to-r from-blue-500/60 via-blue-400/40 to-transparent dark:from-blue-400/60 dark:via-blue-300/40"></div>
            {/* Elemento decorativo lateral mais suave */}
            <div className="absolute left-0 top-0 w-16 h-full bg-gradient-to-r from-blue-500/8 via-blue-400/5 to-transparent dark:from-blue-400/8 dark:via-blue-300/5 rounded-r-2xl"></div>
            {/* Pequeno ponto decorativo */}
            <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-1.5 h-1.5 bg-blue-500 dark:bg-blue-400 rounded-full opacity-60"></div>
          </h2>
        </div>
      );
    },
    // Componente customizado para imagens com título e fonte
    ImageWithCaption: (props: any) => {
      return <ImageWithCaption {...props} />;
    },
    // Componentes customizados para listas que preservam quebras de linha
    li: ({ children }: any) => {
      // Verificar se o conteúdo é apenas texto (sem elementos HTML)
      const isSimpleText = typeof children === 'string';

      if (isSimpleText) {
        // Para texto simples, processar quebras de linha
        const lines = children.split(/\n/);
        const processedContent = lines.map((line: string, index: number) => (
          <React.Fragment key={index}>
            {line}
            {index < lines.length - 1 && <br />}
          </React.Fragment>
        ));

        return (
          <li className="mb-3 text-gray-700 dark:text-gray-300 leading-relaxed pl-1">
            {processedContent}
          </li>
        );
      }

      // Para conteúdo complexo, renderizar normalmente
      return (
        <li className="mb-3 text-gray-700 dark:text-gray-300 leading-relaxed pl-1">
          {children}
        </li>
      );
    },
    // Componente customizado para parágrafos
    p: ({ children }: any) => {
      // Função para processar texto e converter quebras de linha em <br>
      const processText = (text: string) => {
        if (!text || typeof text !== 'string') return text;

        // Dividir por quebras de linha e criar elementos <br>
        const lines = text.split(/\n/);
        return lines.map((line, index) => (
          <React.Fragment key={index}>
            {line}
            {index < lines.length - 1 && <br />}
          </React.Fragment>
        ));
      };

      // Processar todos os children recursivamente
      const processChildren = (child: any): any => {
        if (typeof child === 'string') {
          return processText(child);
        }
        if (React.isValidElement(child) && child.props && child.props.children) {
          return React.cloneElement(child, {
            ...child.props,
            children: React.Children.map(child.props.children, processChildren)
          });
        }
        return child;
      };

      const processedChildren = React.Children.map(children, processChildren);

      return (
        <div>
          {processedChildren}
        </div>
      );
    },
    ul: ({ children }: any) => (
      <ul className="list-disc list-outside space-y-1 my-4 ml-6 pl-2">
        {children}
      </ul>
    ),
    ol: ({ children }: any) => (
      <ol className="list-decimal list-inside space-y-1 my-4 ml-4">
        {children}
      </ol>
    ),
    // Componente customizado para texto em negrito
    strong: ({ children }: any) => (
      <strong className="font-semibold text-gray-900 dark:text-gray-100">
        {children}
      </strong>
    ),
    // Componente customizado para código inline
    code: ({ children }: any) => {
      // Detectar tema escuro
      const isDark = document.documentElement.classList.contains('dark');

      return (
        <code
          className="inline-block"
          style={{
            color: isDark ? 'rgb(229 231 235)' : 'rgb(31 41 55)', // gray-200 : gray-800
            backgroundColor: isDark ? 'rgb(31 41 55)' : 'rgb(243 244 246)', // gray-800 : gray-100
            padding: '0.125rem 0.375rem',
            borderRadius: '0.25rem',
            fontSize: '0.875rem',
            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
            border: isDark ? '1px solid rgb(55 65 81)' : '1px solid rgb(229 231 235)', // gray-700 : gray-200
            fontWeight: '500'
          }}
        >
          {children}
        </code>
      );
    },
  }), []);

  // Extrair todas as imagens do conteúdo
  const extractImagesFromContent = useCallback((content: string) => {
    const imageMatches = content.match(/<ImageWithCaption[^>]*\/>/g) || [];
    return imageMatches.map((match) => {
      const attributes = match.match(/<ImageWithCaption\s+([^>]+)\s*\/>/)?.[1] || '';
      const srcMatch = attributes.match(/src="([^"]+)"/);
      const titleMatch = attributes.match(/title="([^"]+)"/);
      const sourceMatch = attributes.match(/source="([^"]+)"/);
      const globalMatch = attributes.match(/global="([^"]+)"/);

      return {
        src: srcMatch ? srcMatch[1] : '',
        title: titleMatch ? titleMatch[1] : '',
        source: sourceMatch ? sourceMatch[1] : '',
        alt: titleMatch ? titleMatch[1] : '',
        global: globalMatch ? globalMatch[1] === 'true' : false
      };
    });
  }, []);

  // Extrair apenas imagens globais
  const extractGlobalImages = useCallback((content: string) => {
    const allImages = extractImagesFromContent(content);
    return allImages.filter(image => image.global);
  }, [extractImagesFromContent]);

  // Verificar se há imagens globais no conteúdo
  const hasGlobalImages = useMemo(() => {
    const globalImagesFromConducts = extractGlobalImages(conductsContent);
    const globalImagesFromTreatment = hasTreatment ? extractGlobalImages(treatmentContent) : [];
    return globalImagesFromConducts.length > 0 || globalImagesFromTreatment.length > 0;
  }, [conductsContent, treatmentContent, hasTreatment, extractGlobalImages]);

  // 🎯 Listener para botões do header (desktop)
  useEffect(() => {
    const handleTabChangeFromHeader = (event: CustomEvent) => {
      const newTab = event.detail;
      if (newTab && (newTab === 'conducts' || (newTab === 'treatment' && hasTreatment) || (newTab === 'images' && hasGlobalImages))) {
        handleTabChange(newTab);
      }
    };

    window.addEventListener('tabChange', handleTabChangeFromHeader as EventListener);
    return () => window.removeEventListener('tabChange', handleTabChangeFromHeader as EventListener);
  }, [handleTabChange, hasTreatment, hasGlobalImages]);

  const renderMarkdownContent = useCallback((content: string) => {
    // Função para escapar > que não são blockquotes intencionais
    const escapeGreaterThan = (text: string) => {
      // Escapar > quando usado em contexto médico (idade, tempo, valores)
      return text.replace(/(\* .*)> (\d+)/g, '$1&gt; $2') // Em listas: * texto > número
                 .replace(/(\s+)> (\d+)/g, '$1&gt; $2')   // Em sublistas: espaços > número
                 .replace(/(\()> (\d+)/g, '($1&gt; $2')   // Em parênteses: (> número
    };

    const processedContent = escapeGreaterThan(content);

    // Extrair todas as imagens do conteúdo
    const allImages = extractImagesFromContent(processedContent);

    // Estratégia mais simples: encontrar blocos de imagens consecutivas
    // Primeiro, dividir por imagens
    const parts = processedContent.split(/(<ImageWithCaption[^>]*\/>)/g);
    let imageIndex = 0;

    // Agrupar partes em blocos
    const groupedParts = [];
    let i = 0;

    while (i < parts.length) {
      const part = parts[i];
      const isImage = /<ImageWithCaption\s+([^>]+)\s*\/>/.test(part);

      if (isImage) {
        // Começar um grupo de imagens
        const imageGroup = [part];
        i++;

        // Continuar coletando imagens consecutivas (ignorando espaços em branco)
        while (i < parts.length) {
          const nextPart = parts[i];
          const nextIsImage = /<ImageWithCaption\s+([^>]+)\s*\/>/.test(nextPart);

          if (nextIsImage) {
            imageGroup.push(nextPart);
            i++;
          } else if (nextPart.trim() === '') {
            // Ignorar espaços em branco e continuar procurando
            i++;
          } else {
            // Encontrou conteúdo não-imagem, parar o grupo
            break;
          }
        }

        groupedParts.push({ type: 'images', content: imageGroup });
      } else if (part.trim()) {
        // Conteúdo markdown normal
        groupedParts.push({ type: 'markdown', content: part });
        i++;
      } else {
        // Espaço em branco, pular
        i++;
      }
    }

    return (
      <div>
        {groupedParts.map((group, groupIndex) => {
          if (group.type === 'images') {
            // Renderizar grupo de imagens
            const images = group.content;

            if (images.length === 1) {
              // Imagem única - renderização normal
              const imageMatch = images[0].match(/<ImageWithCaption\s+([^>]+)\s*\/>/);
              if (imageMatch) {
                const attributes = imageMatch[1];
                const srcMatch = attributes.match(/src="([^"]+)"/);
                const titleMatch = attributes.match(/title="([^"]+)"/);
                const sourceMatch = attributes.match(/source="([^"]+)"/);

                const src = srcMatch ? srcMatch[1] : '';
                const title = titleMatch ? titleMatch[1] : '';
                const source = sourceMatch ? sourceMatch[1] : '';

                const currentImageIndex = imageIndex++;

                return (
                  <ImageWithCaption
                    key={groupIndex}
                    src={src}
                    title={title}
                    source={source}
                    allImages={allImages}
                    currentIndex={currentImageIndex}
                  />
                );
              }
            } else {
              // Múltiplas imagens - renderização em grid
              return (
                <div key={groupIndex} className="my-8 grid grid-cols-1 sm:grid-cols-2 gap-6">
                  {images.map((imageMarkdown, imageIdx) => {
                    const imageMatch = imageMarkdown.match(/<ImageWithCaption\s+([^>]+)\s*\/>/);
                    if (imageMatch) {
                      const attributes = imageMatch[1];
                      const srcMatch = attributes.match(/src="([^"]+)"/);
                      const titleMatch = attributes.match(/title="([^"]+)"/);
                      const sourceMatch = attributes.match(/source="([^"]+)"/);

                      const src = srcMatch ? srcMatch[1] : '';
                      const title = titleMatch ? titleMatch[1] : '';
                      const source = sourceMatch ? sourceMatch[1] : '';

                      const currentImageIndex = imageIndex++;

                      return (
                        <ImageWithCaption
                          key={imageIdx}
                          src={src}
                          title={title}
                          source={source}
                          allImages={allImages}
                          currentIndex={currentImageIndex}
                          className="my-0" // Remove margem vertical para grid
                        />
                      );
                    }
                    return null;
                  })}
                </div>
              );
            }
          } else {
            // Renderizar markdown normal
            return (
              <ReactMarkdown
                key={groupIndex}
                remarkPlugins={[remarkGfm]}
                className="prose prose-sm max-w-none dark:prose-invert prose-headings:text-gray-900 dark:prose-headings:text-gray-100 prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-li:text-gray-700 dark:prose-li:text-gray-300 prose-code:text-gray-800 dark:prose-code:text-gray-200 prose-code:bg-gray-100 dark:prose-code:bg-gray-800"
                components={markdownComponents}
                skipHtml={true}
              >
                {group.content}
              </ReactMarkdown>
            );
          }

          return null;
        })}
      </div>
    );
  }, [markdownComponents, extractImagesFromContent]);

  const NavigationMenu = () => (
    <div className="space-y-1">
      {sections.map((section, index) => {
        const isActive = activeSection === section.id;
        const isMainSection = section.level === 2;
        const isSubsection = section.level === 3;

        return (
          <div
            key={section.id}
            className={cn(
              "relative group transition-all duration-200",
              isActive ? "transform scale-[1.02]" : "",
              isSubsection ? "ml-4" : ""
            )}
          >
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start text-left h-auto relative rounded-xl transition-all duration-200 border-0 group",
                isMainSection ? "p-3 text-sm" : "p-2 text-xs",
                isActive
                  ? "bg-gradient-to-r from-blue-50 dark:from-blue-900/30 to-indigo-50 dark:to-indigo-900/30 text-blue-700 dark:text-blue-300 font-semibold border border-blue-200 dark:border-blue-800 shadow-sm"
                  : "hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:shadow-sm"
              )}
              onClick={() => scrollToSection(section.id)}
              data-section-id={section.id}
            >
              {/* Indicador lateral */}
              <div className={cn(
                "absolute left-0 top-1/2 -translate-y-1/2 rounded-r-full transition-all duration-300",
                isMainSection ? "w-1 h-8" : "w-0.5 h-5",
                isActive
                  ? "bg-gradient-to-b from-blue-500 dark:from-blue-400 to-indigo-500 dark:to-indigo-400 shadow-sm"
                  : "bg-transparent group-hover:bg-gray-300 dark:group-hover:bg-gray-600"
              )} />

              {/* Conector para subseções */}
              {isSubsection && (
                <div className="absolute -left-4 top-1/2 -translate-y-1/2 w-3 h-px bg-muted dark:bg-gray-600" />
              )}

              <span className={cn(
                "truncate leading-relaxed flex-1",
                isActive ? "font-medium" : "",
                isSubsection ? "text-muted-foreground dark:text-gray-400" : ""
              )}>
                {isSubsection && "• "}{section.title}
              </span>

              {/* Indicador de ativo */}
              {isActive && (
                <div className="flex-shrink-0 w-2.5 h-2.5 bg-gradient-to-r from-blue-500 dark:from-blue-400 to-indigo-500 dark:to-indigo-400 rounded-full shadow-sm animate-pulse" />
              )}
            </Button>
          </div>
        );
      })}
    </div>
  );

  const MobileMenu = () => (
    <div className="w-full max-w-full overflow-hidden">
      <Accordion type="single" collapsible className="w-full max-w-full">
        <AccordionItem value="navigation" className="border-0 bg-transparent max-w-full">
            <AccordionTrigger className="!p-0 !py-0 !px-0 !border-0 !bg-transparent hover:!bg-transparent hover:no-underline !mb-0 !rounded-none">
              <div className="flex items-center gap-2 w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-colors max-w-full">
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                <div className="flex-1 min-w-0 overflow-hidden">
                  {activeSectionTitle ? (
                    <div className="text-left min-w-0 overflow-hidden">
                      <div className="font-medium text-gray-900 text-sm truncate block min-w-0 max-w-full">
                        {activeSectionTitle.length > 30 ? `${activeSectionTitle.substring(0, 30)}...` : activeSectionTitle}
                      </div>
                      <div className="text-xs text-blue-600">Lendo agora</div>
                    </div>
                  ) : (
                    <span className="font-medium text-gray-900 text-sm truncate block min-w-0">📋 Navegação</span>
                  )}
                </div>
                <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full font-medium flex-shrink-0 ml-2">
                  {sections.length}
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-4 pb-4">
              <div className="max-h-80 overflow-y-auto space-y-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {sections.map((section, index) => {
                  const isActive = activeSection === section.id;
                  const isMainSection = section.level === 2;
                  const isSubsection = section.level === 3;

                  return (
                    <Button
                      key={section.id}
                      variant="ghost"
                      className={cn(
                        "w-full justify-start h-auto text-left rounded-2xl transition-all duration-300 border-0 transform hover:scale-[1.02]",
                        isMainSection ? "p-3 text-sm" : "p-2 text-xs ml-4",
                        isActive
                          ? "bg-gradient-to-r from-blue-50 via-blue-50/80 to-indigo-50/60 text-blue-700 font-semibold border border-blue-200/60 shadow-sm"
                          : "hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-50/50 text-gray-700 hover:text-gray-900 hover:shadow-sm"
                      )}
                      onClick={() => scrollToSection(section.id, true)}
                      data-section-id={section.id}
                    >
                      <div className="flex items-center gap-3 w-full">
                        <span className={cn(
                          "truncate flex-1",
                          isSubsection ? "text-muted-foreground" : ""
                        )}>
                          {isSubsection && "• "}{section.title}
                        </span>
                        {isActive && (
                          <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full animate-pulse shadow-sm" />
                        )}
                      </div>
                    </Button>
                  );
                })}
              </div>
            </AccordionContent>
          </AccordionItem>
      </Accordion>
    </div>
  );

  return (
    <div className="h-full flex flex-col overflow-hidden relative">
      {/* Indicador de Seção Ativa - Integrado ao header */}
      {activeSectionTitle && activeTab !== 'images' && (
        <div className="flex-shrink-0 w-full">
          <div className="w-full px-4 lg:container lg:mx-auto lg:max-w-7xl lg:px-6 py-0.5">
            <button
              onClick={() => setSummaryDialogOpen(true)}
              className="w-full bg-white/90 dark:bg-gray-900/90 rounded-2xl px-4 py-1.5 border border-blue-200/60 dark:border-blue-800/60 shadow-md backdrop-blur-sm hover:bg-white dark:hover:bg-gray-800 transition-colors duration-150 hover:shadow-lg"
            >
              <div className="flex items-center gap-3 min-w-0">
                <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-blue-400 dark:from-blue-400 dark:to-blue-300 rounded-full animate-pulse shadow-sm flex-shrink-0"></div>
                <span className="text-sm font-medium text-blue-700 dark:text-blue-300 truncate">
                  Lendo agora: {activeSectionTitle}
                </span>
                <List className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0 ml-auto" />
              </div>
            </button>
          </div>
        </div>
      )}
      {/* Área Principal - Conteúdo Clean */}
      <div className="flex-1 overflow-hidden">
        <div className="w-full px-4 lg:container lg:mx-auto lg:max-w-7xl lg:px-6 pt-1 pb-2 h-full">
          <div className="bg-white/80 dark:bg-gray-900/80 rounded-3xl border border-gray-200/50 dark:border-gray-700/50 shadow-lg backdrop-blur-sm h-full overflow-hidden flex flex-col">
            <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full h-full flex flex-col">
              <TabsContent value="conducts" className="mt-0 flex-1 overflow-y-auto content-scroll-area p-6 lg:p-8">
                <div className="prose prose-lg max-w-none w-full dark:prose-invert
                  prose-headings:text-gray-900 dark:prose-headings:text-gray-100 prose-headings:font-bold
                  prose-h2:text-3xl prose-h2:border-0 prose-h2:pb-4 prose-h2:mb-8 prose-h2:mt-0 prose-h2:text-blue-900 dark:prose-h2:text-blue-300
                  prose-h3:text-xl prose-h3:text-gray-800 dark:prose-h3:text-gray-200 prose-h3:mt-6 prose-h3:mb-4 prose-h3:font-semibold
                  prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-p:leading-relaxed prose-p:mb-6 prose-p:text-lg
                  prose-li:text-gray-700 dark:prose-li:text-gray-300 prose-li:mb-2 prose-li:text-lg prose-li:leading-relaxed
                  prose-ul:space-y-2 prose-ol:space-y-2
                  prose-strong:text-gray-900 dark:prose-strong:text-gray-100 prose-strong:font-semibold
                  prose-code:bg-blue-100/80 dark:prose-code:bg-blue-900/30 prose-code:px-2.5 prose-code:py-1 prose-code:rounded-lg prose-code:text-sm prose-code:text-blue-800 dark:prose-code:text-blue-300 prose-code:border prose-code:border-blue-200/50 dark:prose-code:border-blue-800/50
                  [&>*:first-child]:mt-0
                  [&>h2:first-child]:mt-0
                  [&>h3:first-child]:mt-0
                ">
                  {renderMarkdownContent(conductsContent)}
                </div>
              </TabsContent>

              {hasTreatment && (
                <TabsContent value="treatment" className="mt-0 flex-1 overflow-y-auto content-scroll-area p-6 lg:p-8">
                  <div className="prose prose-lg max-w-none w-full dark:prose-invert
                    prose-headings:text-gray-900 dark:prose-headings:text-gray-100 prose-headings:font-bold
                    prose-h2:text-3xl prose-h2:border-0 prose-h2:pb-4 prose-h2:mb-8 prose-h2:mt-0 prose-h2:text-green-900 dark:prose-h2:text-green-300
                    prose-h3:text-xl prose-h3:text-gray-800 dark:prose-h3:text-gray-200 prose-h3:mt-6 prose-h3:mb-4 prose-h3:font-semibold
                    prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-p:leading-relaxed prose-p:mb-6 prose-p:text-lg
                    prose-li:text-gray-700 dark:prose-li:text-gray-300 prose-li:mb-2 prose-li:text-lg prose-li:leading-relaxed
                    prose-ul:space-y-2 prose-ol:space-y-2
                    prose-strong:text-gray-900 dark:prose-strong:text-gray-100 prose-strong:font-semibold
                    prose-code:bg-green-100/80 dark:prose-code:bg-green-900/30 prose-code:px-2.5 prose-code:py-1 prose-code:rounded-lg prose-code:text-sm prose-code:text-green-800 dark:prose-code:text-green-300 prose-code:border prose-code:border-green-200/50 dark:prose-code:border-green-800/50
                    [&>*:first-child]:mt-0
                    [&>h2:first-child]:mt-0
                    [&>h3:first-child]:mt-0
                  ">
                    {renderMarkdownContent(treatmentContent)}
                  </div>
                </TabsContent>
              )}

              {/* Aba de Imagens - só aparece se houver imagens globais */}
              {hasGlobalImages && (
                <TabsContent value="images" className="mt-0 flex-1 overflow-y-auto content-scroll-area p-6 lg:p-8">
                  <div className="w-full">
                    {(() => {
                      const globalImagesFromConducts = extractGlobalImages(conductsContent);
                      const globalImagesFromTreatment = hasTreatment ? extractGlobalImages(treatmentContent) : [];
                      const allGlobalImages = [...globalImagesFromConducts, ...globalImagesFromTreatment];

                      return (
                        <ImageGallery
                          images={allGlobalImages}
                          className="py-4"
                        />
                      );
                    })()}
                  </div>
                </TabsContent>
              )}
            </Tabs>
          </div>
        </div>
      </div>

      {/* Indicadores de Progresso Discretos - Apenas Desktop */}
      <div className="hidden lg:flex flex-shrink-0">
        <div className="w-full lg:max-w-6xl lg:mx-auto px-4 lg:px-6 pb-4">
          <div className="bg-gradient-to-t from-white via-white to-white/90 dark:from-gray-900 dark:via-gray-900 dark:to-gray-900/90 border border-gray-200/30 dark:border-gray-700/30 rounded-2xl py-3 px-6 backdrop-blur-sm shadow-sm">
            <div className="flex items-center justify-center gap-4">
            {activeTab === 'images' ? (
              // Indicador para aba de imagens
              <>
                {/* Pontos representando imagens */}
                <div className="flex items-center gap-2">
                  {(() => {
                    const globalImagesFromConducts = extractGlobalImages(conductsContent);
                    const globalImagesFromTreatment = hasTreatment ? extractGlobalImages(treatmentContent) : [];
                    const allGlobalImages = [...globalImagesFromConducts, ...globalImagesFromTreatment];

                    return allGlobalImages.map((_, index) => (
                      <div
                        key={index}
                        className="w-3 h-3 rounded-full bg-purple-500 dark:bg-purple-400 shadow-sm"
                        title={`Imagem ${index + 1}`}
                      />
                    ));
                  })()}
                </div>

                {/* Contador de imagens */}
                <div className="text-sm text-gray-500 dark:text-gray-400 ml-4">
                  {(() => {
                    const globalImagesFromConducts = extractGlobalImages(conductsContent);
                    const globalImagesFromTreatment = hasTreatment ? extractGlobalImages(treatmentContent) : [];
                    const totalImages = globalImagesFromConducts.length + globalImagesFromTreatment.length;
                    return `${totalImages} ${totalImages === 1 ? 'imagem' : 'imagens'}`;
                  })()}
                </div>
              </>
            ) : (
              // Indicador para abas de conteúdo (condutas/tratamento)
              <>
                {/* Pontos de Progresso */}
                <div className="flex items-center gap-2">
                  {sections.map((section, index) => {
                    const isActive = activeSection === section.id;
                    const isPassed = sections.findIndex(s => s.id === activeSection) > index;
                    return (
                      <button
                        key={section.id}
                        onClick={() => scrollToSection(section.id)}
                        className={`w-3 h-3 rounded-full transition-all hover:scale-110 ${
                          isActive
                            ? 'bg-blue-500 dark:bg-blue-400 ring-2 ring-blue-200 dark:ring-blue-800'
                            : isPassed
                            ? 'bg-green-500 dark:bg-green-400'
                            : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                        }`}
                        title={section.title}
                      />
                    );
                  })}
                </div>

                {/* Contador de seções */}
                <div className="text-sm text-gray-500 dark:text-gray-400 ml-4">
                  {sections.findIndex(s => s.id === activeSection) + 1} / {sections.length} seções
                </div>
              </>
            )}
            </div>
          </div>
        </div>
      </div>

      {/* Dialog do Sumário */}
      <Dialog open={summaryDialogOpen} onOpenChange={setSummaryDialogOpen}>
        <DialogContent className="max-w-[95dvw] max-h-[85dvh] w-full h-full rounded-3xl border-2 border-gray-200 dark:border-gray-700 shadow-2xl overflow-hidden">
          <DialogHeader className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <DialogTitle className="flex items-center gap-3 text-xl font-bold text-gray-900 dark:text-gray-100">
              <List className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              Sumário
            </DialogTitle>
            <DialogDescription className="text-gray-600 dark:text-gray-400 mt-2">
              Navegue rapidamente pelas seções do conteúdo
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto p-4 lg:p-6">
            <div className="space-y-2 lg:space-y-3">
              {sections.map((section, index) => (
                <button
                  key={section.id}
                  onClick={() => navigateToSection(section.id)}
                  className={`w-full text-left p-3 lg:p-4 rounded-xl border transition-colors duration-150 hover:shadow-sm ${
                    activeSection === section.id
                      ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 shadow-sm'
                      : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-7 h-7 lg:w-8 lg:h-8 rounded-full flex items-center justify-center text-xs lg:text-sm font-bold ${
                      activeSection === section.id
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                    }`}>
                      {index + 1}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className={`font-semibold text-sm lg:text-base leading-tight ${
                        activeSection === section.id
                          ? 'text-blue-900 dark:text-blue-100'
                          : 'text-gray-900 dark:text-gray-100'
                      }`}>
                        {section.title}
                      </h3>
                    </div>
                    <ChevronRight className={`w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0 ${
                      activeSection === section.id
                        ? 'text-blue-600 dark:text-blue-400'
                        : 'text-gray-400 dark:text-gray-500'
                    }`} />
                  </div>
                </button>
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
