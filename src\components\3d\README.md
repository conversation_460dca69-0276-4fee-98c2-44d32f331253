# 🎬 Experiência Cinematográfica PedBook

Uma experiência imersiva e cinematográfica para apresentar a plataforma de estudos PedBook aos usuários pela primeira vez.

## 🌟 Visão Geral

A experiência cinematográfica é uma sequência de apresentação interativa que inclui:

- **Campo de estrelas animado** com partículas em movimento
- **Envelope/carta espacial** que chega flutuando e se abre
- **Contador animado** mostrando a posição do usuário
- **Showcase de recursos** com efeitos 3D
- **Efeitos sonoros** sincronizados (Web Audio API)
- **Partículas de celebração** e confetti
- **Transições cinematográficas** entre estágios

## 🎭 Estágios da Experiência

### 1. **Starfield** (2s)
- Campo de estrelas animado de fundo
- Logo PedBook aparece gradualmente
- Texto "Preparando sua experiência..."

### 2. **Envelope Arrival** (3s)
- Envelope espacial chega flutuando
- Partículas mágicas ao redor
- Som de chegada mágica

### 3. **Envelope Opening** (Interativo)
- Usuário clica para abrir o envelope
- Animações de abertura
- Hint visual para interação

### 4. **Welcome Message** (Interativo)
- Mensagem de parabéns
- Número personalizado (61.798.145)
- Botão "Vamos começar!"

### 5. **Counter Animation** (3s)
- Contador animado até o número final
- Efeitos de confetti
- Som de contador

### 6. **Feature Showcase** (6s)
- Apresentação dos recursos da plataforma
- Cards 3D com animações
- Partículas de celebração

### 7. **Final CTA** (Interativo)
- Call-to-action final
- Opções para começar ou explorar
- Transição para a plataforma

## 🎵 Sistema de Áudio

### SoundManager
- **Web Audio API** para efeitos sonoros
- Sons sincronizados com cada estágio
- Controle de volume e mute
- Efeitos: ambiente, chegada, abertura, celebração, contador

### Efeitos Disponíveis
- `playClick()` - Som de clique
- `playHover()` - Som de hover
- `playSuccess()` - Som de sucesso
- Sons automáticos por estágio

## ✨ Componentes de Partículas

### StarField
- **Canvas customizado** + **tsParticles**
- Estrelas com movimento e brilho
- Nebulosas de fundo
- Estrelas cadentes ocasionais

### CelebrationParticles
- Partículas de celebração configuráveis
- Intensidade: low, medium, high
- Cores personalizáveis
- Emissores múltiplos

### ConfettiExplosion
- Explosão de confetti
- Física realista com gravidade
- Formas variadas (quadrados, triângulos)
- Rotação e vida útil

### ShootingStars
- Estrelas cadentes com trilha
- Movimento diagonal
- Aparição aleatória

## 🎨 Tecnologias Utilizadas

- **React** + **TypeScript**
- **Framer Motion** - Animações
- **tsParticles** - Sistema de partículas
- **Web Audio API** - Efeitos sonoros
- **Tailwind CSS** - Estilização
- **Canvas API** - Efeitos customizados

## 🚀 Como Usar

### Integração Básica
```tsx
import { CinematicWelcome } from "@/components/3d/CinematicWelcome";

function App() {
  const [showCinematic, setShowCinematic] = useState(true);

  return (
    <>
      {showCinematic && (
        <CinematicWelcome
          onComplete={() => setShowCinematic(false)}
          onStartStudy={() => navigate('/estudos/filtros')}
        />
      )}
    </>
  );
}
```

### Controles Disponíveis
- **Botão de áudio** (canto superior direito)
- **Botão "Pular"** (canto superior esquerdo)
- **Interações** (cliques nos elementos)

## 🎯 Configurações

### Personalização de Cores
```tsx
<CelebrationParticles
  colors={['#3b82f6', '#8b5cf6', '#ec4899']}
  intensity="high"
/>
```

### Controle de Áudio
```tsx
<SoundManager
  isEnabled={audioEnabled}
  currentStage={stage}
/>
```

## 📱 Responsividade

- **Desktop**: Experiência completa
- **Mobile**: Adaptações para toque
- **Tablet**: Layout otimizado

## ⚡ Performance

- **Lazy loading** de componentes
- **Otimização de partículas** baseada no dispositivo
- **Cleanup automático** de recursos
- **FPS limitado** para economia de bateria

## 🔧 Manutenção

### Adicionando Novos Estágios
1. Adicionar ao tipo `CinematicStage`
2. Implementar lógica no `useEffect`
3. Adicionar caso no `AnimatePresence`
4. Configurar sons no `SoundManager`

### Novos Efeitos de Partículas
1. Criar componente em `CelebrationParticles.tsx`
2. Configurar opções do tsParticles
3. Integrar nos estágios desejados

## 🎪 Inspiração

Baseado em experiências cinematográficas de:
- **Canva** - Apresentações interativas
- **Apple** - Eventos de lançamento
- **Stripe** - Onboarding premium
- **Linear** - Animações fluidas

## 🏆 Resultado

Uma experiência que impressiona e engaja o usuário desde o primeiro contato, estabelecendo o PedBook como uma plataforma premium e inovadora para estudos médicos.
