import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

export const useRecoveryLogger = () => {
  const location = useLocation();

  useEffect(() => {
    // Log sempre que a URL mudar
    // Log navegação para debug (removido em produção)

    // Detectar URLs de recuperação
    const urlParams = new URLSearchParams(location.search);
    const token = urlParams.get('token');
    const type = urlParams.get('type');
    const code = urlParams.get('code'); // PKCE code (fallback)
    const accessToken = urlParams.get('access_token');
    const refreshToken = urlParams.get('refresh_token');

    // Detectar tokens de recuperação (logs removidos em produção)
    if (token || accessToken || code) {
      // Processar tokens de recuperação silenciosamente
    }

    // Detectar hash fragments (usado pelo Supabase)
    if (location.hash) {
      console.log('🔗 [RECOVERY-LOGGER] Hash fragment detectado:', location.hash);
      
      // Parse hash parameters
      const hashParams = new URLSearchParams(location.hash.substring(1));
      const hashAccessToken = hashParams.get('access_token');
      const hashRefreshToken = hashParams.get('refresh_token');
      const hashType = hashParams.get('type');
      
      if (hashAccessToken) {
        console.log('🔑 [RECOVERY-LOGGER] TOKENS NO HASH DETECTADOS!');
        console.log('  - access_token:', `${hashAccessToken.substring(0, 20)}...`);
        console.log('  - refresh_token:', hashRefreshToken ? `${hashRefreshToken.substring(0, 20)}...` : 'null');
        console.log('  - type:', hashType);
      }
    }

    // Detectar páginas de recuperação silenciosamente

  }, [location]);
};
