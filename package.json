{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && node scripts/inline-css.js", "build:dev": "vite build --mode development", "build:prerender": "node scripts/testBuild.js", "build:ssr": "node scripts/advancedSSR.js", "build:seo": "node scripts/fixSEO.js", "generate:routes": "node scripts/generateRoutes.js", "generate:new-sections": "node scripts/generateNewSections.js", "lint": "eslint .", "preview": "vite preview", "vercel-build": "node scripts/buildComplete.js", "start": "node server.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dismissable-layer": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@sentry/react": "^9.33.0", "@sentry/tracing": "^7.120.3", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.56.2", "@tiptap/extension-color": "^2.10.4", "@tiptap/extension-image": "^2.11.3", "@tiptap/extension-link": "^2.10.3", "@tiptap/extension-text-style": "^2.10.4", "@tiptap/extension-underline": "^2.10.4", "@tiptap/pm": "^2.10.3", "@tiptap/react": "^2.10.4", "@tiptap/starter-kit": "^2.10.4", "@tsparticles/basic": "^3.8.1", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@types/html2canvas": "^0.5.35", "@types/md5": "^2.3.5", "@types/react-helmet": "^6.1.11", "@uiw/react-md-editor": "^4.0.7", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "express": "^4.21.2", "framer-motion": "^11.18.2", "fuse.js": "^7.1.0", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "jspdf": "^3.0.1", "lottie-react": "^2.4.1", "lovable-tagger": "^1.0.19", "lucide-react": "^0.451.0", "md5": "^2.3.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.15.3", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwind-scrollbar": "^3.1.0", "tailwindcss-animate": "^1.0.7", "tsparticles": "^3.8.1", "uuid": "^11.0.5", "vaul": "^0.9.3", "zod": "^3.23.8", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "canvas": "^3.1.0", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "puppeteer": "^24.9.0", "tailwindcss": "^3.4.11", "terser": "^5.39.2", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.19"}}