
import React from 'react';
import ReactDOM from 'react-dom/client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { DEFAULT_QUERY_CONFIG } from '@/utils/cacheConfig';
import { persistentCache } from '@/utils/persistentCache';
import App from './App';
import './index.css';
import './unregister-sw.js';

// Configuração do Sentry
import * as Sentry from "@sentry/react";

Sentry.init({
  dsn: "COLE_SUA_DSN_AQUI", // Substitua pela sua DSN do Sentry
  environment: import.meta.env.MODE, // "development" ou "production"
  tracesSampleRate: 1.0,
  beforeSend(event) {
    // Filtrar erros de desenvolvimento
    if (import.meta.env.MODE === 'development') {
      return null; // Não enviar erros em desenvolvimento
    }
    return event;
  },
});

// Criar QueryClient com configuração otimizada
const queryClient = new QueryClient({
  ...DEFAULT_QUERY_CONFIG
});

// Inicializar cache persistente
persistentCache.initializeWithQueryClient(queryClient);
persistentCache.setupAutoSave(queryClient);

// Limpar cache persistente de medicamentos para garantir dados atualizados
persistentCache.remove(JSON.stringify(['medications']));
persistentCache.remove(JSON.stringify(['medication-categories']));

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  </React.StrictMode>,
);
