import React from "react";

let renderCount = 0;

const ConductsTestMinimal = React.memo(() => {
  renderCount += 1;
  console.log('🧪 [TEST] ConductsTestMinimal render count:', renderCount);
  
  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold">Teste Minimal</h1>
        <p>Render count: {renderCount}</p>
        <p>Se este número aumenta constantemente, o problema é sistêmico.</p>
      </div>
    </div>
  );
});

ConductsTestMinimal.displayName = 'ConductsTestMinimal';

export default ConductsTestMinimal;
