import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Building2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  XCircle,
  Baby,
  User,
  Shield,
  DollarSign,
  Calendar,
  Hash,
  Loader2
} from "lucide-react";

interface ActiveIngredient {
  id: string;
  name: string;
  dcb_code: string | null;
  cas_number: string | null;
  created_at: string;
  updated_at: string;
  // Dados carregados da RPC
  total_drugs?: number;
  total_presentations?: number;
  total_therapeutic_classes?: number;
  total_administration_routes?: number;
  therapeutic_classes?: string[];
  administration_routes?: string[];
  has_combinations?: boolean;
  combination_count?: number;
  is_association_only?: boolean;
  individual_ingredients?: string[];
  dcb_codes?: string[];
  cas_numbers?: string[];
}

interface DrugData {
  id: string;
  brand_name: string;
  descricao: string | null;
  fabricante: string | null;
  composicao: string | null;
  pregnancy_info: string | null;
  breastfeeding_info: string | null;
  patient_instructions: string[] | null;
  adult_use: boolean | null;
  pediatric_use: boolean | null;
  is_controlled: boolean;
  is_high_cost: boolean;
  status: string;
  tipo: string | null;
  titularidade: string | null;
  eans: string[] | null;
  is_association: boolean | null;
  presentations?: DrugPresentation[];
  therapeutic_classes?: string[]; // Classes terapêuticas da nova estrutura
}

interface DrugPresentation {
  id: string;
  strength: string;
  dosage_form: string;
  prescription_category: string;
  prescription_type: string;
  therapeutic_classes: string[];
  administration_routes: string[];
  is_primary: boolean;
}

interface ActiveIngredientDetailsProps {
  ingredient: ActiveIngredient;
  relatedDrugs: DrugData[];
  isLoadingDrugs: boolean;
}

export const ActiveIngredientDetails: React.FC<ActiveIngredientDetailsProps> = ({
  ingredient,
  relatedDrugs,
  isLoadingDrugs
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'ativo': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      'inativo': { color: 'bg-red-100 text-red-800', icon: XCircle },
      'pendente': { color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.ativo;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status}
      </Badge>
    );
  };

  const analyzeDataQuality = () => {
    const issues: string[] = [];
    const suggestions: string[] = [];

    relatedDrugs.forEach(drug => {
      if (!drug || !drug.brand_name) return;

      // Verificar campos obrigatórios vazios
      if (!drug.descricao || drug.descricao.trim() === '') {
        issues.push(`${drug.brand_name}: Descrição vazia`);
      }

      if (!drug.therapeutic_classes || drug.therapeutic_classes.length === 0) {
        issues.push(`${drug.brand_name}: Classe terapêutica não definida`);
      }

      if (!drug.fabricante || drug.fabricante.trim() === '') {
        suggestions.push(`${drug.brand_name}: Adicionar fabricante`);
      }

      // Verificar informações de gravidez e amamentação
      if (!drug.pregnancy_info || drug.pregnancy_info === 'nd' || drug.pregnancy_info.trim() === '') {
        suggestions.push(`${drug.brand_name}: Adicionar informações sobre gravidez`);
      }

      if (!drug.breastfeeding_info || drug.breastfeeding_info === 'nd' || drug.breastfeeding_info.trim() === '') {
        suggestions.push(`${drug.brand_name}: Adicionar informações sobre amamentação`);
      }

      // Verificar instruções ao paciente
      if (!drug.patient_instructions || drug.patient_instructions.length === 0) {
        suggestions.push(`${drug.brand_name}: Adicionar instruções ao paciente`);
      }
    });

    return { issues, suggestions };
  };

  const { issues, suggestions } = analyzeDataQuality();

  return (
    <div className="space-y-6">
      {/* Informações Básicas do Princípio Ativo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Informações Básicas
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-600">Nome</label>
              <p className="text-lg font-semibold">{ingredient.name}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600">Código DCB</label>
              {ingredient.dcb_codes && ingredient.dcb_codes.length > 1 ? (
                <div className="flex flex-wrap gap-1 mt-1">
                  {ingredient.dcb_codes.filter(Boolean).map((dcb, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {dcb}
                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  {ingredient.dcb_code || 'Não informado'}
                </p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium text-gray-600">Número CAS</label>
              {ingredient.cas_numbers && ingredient.cas_numbers.length > 1 ? (
                <div className="flex flex-wrap gap-1 mt-1">
                  {ingredient.cas_numbers.filter(Boolean).map((cas, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {cas}
                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  {ingredient.cas_number || 'Não informado'}
                </p>
              )}
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600">Total de Medicamentos</label>
              <p className="flex items-center gap-2">
                <Pill className="h-4 w-4" />
                {ingredient.total_drugs || 0}
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-600">Total de Apresentações</label>
              <p className="flex items-center gap-2">
                <Building2 className="h-4 w-4" />
                {ingredient.total_presentations || 0}
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-600">Classes Terapêuticas</label>
              <p className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                {ingredient.total_therapeutic_classes || 0}
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-600">Vias de Administração</label>
              <p className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                {ingredient.total_administration_routes || 0}
              </p>
            </div>

            {ingredient.has_combinations && (
              <div>
                <label className="text-sm font-medium text-gray-600">Associações</label>
                <p className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  {ingredient.combination_count || 0} medicamentos
                </p>
              </div>
            )}

            {ingredient.individual_ingredients && ingredient.individual_ingredients.length > 1 && (
              <div className="col-span-2">
                <label className="text-sm font-medium text-gray-600">Ingredientes da Combinação</label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {ingredient.individual_ingredients.map((ing, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {ing}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {ingredient.is_association_only && (
              <div className="col-span-2">
                <Badge variant="secondary" className="bg-orange-100 text-orange-700 border-orange-300">
                  🔗 Esta é uma combinação de medicamentos
                </Badge>
              </div>
            )}
          </div>

          <Separator />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <label className="text-sm font-medium text-gray-600">Criado em</label>
              <p className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                {formatDate(ingredient.created_at)}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Atualizado em</label>
              <p className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                {formatDate(ingredient.updated_at)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Classes Terapêuticas e Vias de Administração */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Classes Terapêuticas */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Classes Terapêuticas ({ingredient.therapeutic_classes?.length ?? '?'})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!ingredient.therapeutic_classes || ingredient.therapeutic_classes.length === 0 ? (
              <p className="text-gray-500 text-center py-4">
                Nenhuma classe terapêutica encontrada
              </p>
            ) : (
              <div className="space-y-2">
                {ingredient.therapeutic_classes.map((className, index) => (
                  <Badge key={index} variant="outline" className="mr-2 mb-2">
                    {className}
                  </Badge>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Vias de Administração */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Vias de Administração ({ingredient.administration_routes?.length ?? '?'})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!ingredient.administration_routes || ingredient.administration_routes.length === 0 ? (
              <p className="text-gray-500 text-center py-4">
                Nenhuma via de administração encontrada
              </p>
            ) : (
              <div className="space-y-2">
                {ingredient.administration_routes.map((route, index) => (
                  <Badge key={index} variant="secondary" className="mr-2 mb-2">
                    {route}
                  </Badge>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Análise de Qualidade dos Dados */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Análise de Qualidade dos Dados
          </CardTitle>
          <CardDescription>
            Problemas e sugestões identificados nos medicamentos relacionados
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {issues.length > 0 && (
            <div>
              <h4 className="font-medium text-red-700 mb-2 flex items-center gap-2">
                <XCircle className="h-4 w-4" />
                Problemas Críticos ({issues.length})
              </h4>
              <ul className="space-y-1">
                {issues.slice(0, 5).map((issue, index) => (
                  <li key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                    • {issue}
                  </li>
                ))}
                {issues.length > 5 && (
                  <li className="text-sm text-red-500 italic">
                    ... e mais {issues.length - 5} problemas
                  </li>
                )}
              </ul>
            </div>
          )}

          {suggestions.length > 0 && (
            <div>
              <h4 className="font-medium text-yellow-700 mb-2 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                Sugestões de Melhoria ({suggestions.length})
              </h4>
              <ul className="space-y-1">
                {suggestions.slice(0, 5).map((suggestion, index) => (
                  <li key={index} className="text-sm text-yellow-600 bg-yellow-50 p-2 rounded">
                    • {suggestion}
                  </li>
                ))}
                {suggestions.length > 5 && (
                  <li className="text-sm text-yellow-500 italic">
                    ... e mais {suggestions.length - 5} sugestões
                  </li>
                )}
              </ul>
            </div>
          )}

          {issues.length === 0 && suggestions.length === 0 && (
            <div className="text-center py-4">
              <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <p className="text-green-600 font-medium">
                Todos os dados estão em boa qualidade!
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Resumo Estatístico dos Medicamentos */}
      {relatedDrugs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              Resumo dos Medicamentos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {relatedDrugs.length}
                </div>
                <div className="text-xs text-gray-500">Medicamentos</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {relatedDrugs.reduce((acc, drug) => acc + (drug.presentations?.length || 0), 0)}
                </div>
                <div className="text-xs text-gray-500">Apresentações</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {relatedDrugs.filter(drug => drug.is_controlled).length}
                </div>
                <div className="text-xs text-gray-500">Controlados</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {relatedDrugs.filter(drug => drug.is_high_cost).length}
                </div>
                <div className="text-xs text-gray-500">Alto Custo</div>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 pt-4 border-t">
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-500">
                  {relatedDrugs.filter(drug => drug.adult_use).length}
                </div>
                <div className="text-xs text-gray-500">Uso Adulto</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-pink-500">
                  {relatedDrugs.filter(drug => drug.pediatric_use).length}
                </div>
                <div className="text-xs text-gray-500">Uso Pediátrico</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-red-500">
                  {relatedDrugs.filter(drug => drug.pregnancy_info && drug.pregnancy_info !== 'nd').length}
                </div>
                <div className="text-xs text-gray-500">Info Gravidez</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-cyan-500">
                  {relatedDrugs.filter(drug => drug.breastfeeding_info && drug.breastfeeding_info !== 'nd').length}
                </div>
                <div className="text-xs text-gray-500">Info Amamentação</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Lista de Medicamentos Relacionados */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Pill className="h-5 w-5" />
            Medicamentos Relacionados ({relatedDrugs.length})
          </CardTitle>
          <CardDescription>
            Medicamentos que contêm este princípio ativo
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingDrugs ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Carregando medicamentos...</span>
            </div>
          ) : relatedDrugs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Pill className="h-8 w-8 mx-auto mb-2" />
              <p>Nenhum medicamento encontrado</p>
            </div>
          ) : (
            <div className="space-y-6 max-h-[80vh] overflow-y-auto">
              {relatedDrugs.map((drug) => (
                <Card key={drug.id} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-6">
                    {/* Cabeçalho do Medicamento */}
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h4 className="font-medium text-xl mb-1">{drug.brand_name}</h4>
                        {drug.descricao && drug.descricao !== drug.brand_name && (
                          <p className="text-sm text-gray-600">{drug.descricao}</p>
                        )}
                      </div>
                      {getStatusBadge(drug.status)}
                    </div>

                    {/* Informações Básicas */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                      {drug.therapeutic_classes && drug.therapeutic_classes.length > 0 && (
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Classes Terapêuticas</label>
                          <div className="flex flex-wrap gap-1">
                            {drug.therapeutic_classes.map((tc, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                <Building2 className="h-3 w-3 mr-1" />
                                {tc}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {drug.fabricante && (
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Fabricante</label>
                          <p className="flex items-center gap-1 text-sm">
                            <Building2 className="h-3 w-3" />
                            {drug.fabricante}
                          </p>
                        </div>
                      )}

                      {drug.tipo && (
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Tipo</label>
                          <p className="text-sm capitalize">{drug.tipo.replace('-', ' ')}</p>
                        </div>
                      )}

                      {drug.titularidade && (
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Titularidade</label>
                          <p className="text-sm">{drug.titularidade}</p>
                        </div>
                      )}
                    </div>

                    {/* Composição */}
                    {drug.composicao && (
                      <div className="mb-4">
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Composição</label>
                        <p className="text-sm bg-gray-50 p-2 rounded mt-1">{drug.composicao}</p>
                      </div>
                    )}

                    {/* Tags de Uso */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {drug.adult_use && (
                        <Badge variant="outline" className="text-xs">
                          <User className="h-3 w-3 mr-1" />
                          Uso Adulto
                        </Badge>
                      )}
                      {drug.pediatric_use && (
                        <Badge variant="outline" className="text-xs">
                          <Baby className="h-3 w-3 mr-1" />
                          Uso Pediátrico
                        </Badge>
                      )}
                      {drug.is_controlled && (
                        <Badge variant="destructive" className="text-xs">
                          <Shield className="h-3 w-3 mr-1" />
                          Medicamento Controlado
                        </Badge>
                      )}
                      {drug.is_high_cost && (
                        <Badge variant="secondary" className="text-xs">
                          <DollarSign className="h-3 w-3 mr-1" />
                          Alto Custo
                        </Badge>
                      )}
                      {drug.is_association && (
                        <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700 border-orange-300">
                          Associação
                        </Badge>
                      )}
                    </div>

                    {/* Informações de Gravidez e Amamentação */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      {drug.pregnancy_info && drug.pregnancy_info !== 'nd' && (
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Gravidez</label>
                          <p className="text-sm bg-pink-50 p-2 rounded mt-1">{drug.pregnancy_info}</p>
                        </div>
                      )}

                      {drug.breastfeeding_info && drug.breastfeeding_info !== 'nd' && (
                        <div>
                          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Amamentação</label>
                          <p className="text-sm bg-blue-50 p-2 rounded mt-1">{drug.breastfeeding_info}</p>
                        </div>
                      )}
                    </div>

                    {/* Instruções ao Paciente */}
                    {drug.patient_instructions && drug.patient_instructions.length > 0 && (
                      <div className="mb-4">
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Instruções ao Paciente</label>
                        <ul className="text-sm bg-green-50 p-2 rounded mt-1 list-disc list-inside">
                          {drug.patient_instructions.map((instruction, idx) => (
                            <li key={idx}>{instruction}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* EANs */}
                    {drug.eans && drug.eans.length > 0 && (
                      <div className="mb-4">
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Códigos EAN</label>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {drug.eans.map((ean, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs font-mono">
                              {ean}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Apresentações Detalhadas */}
                    {drug.presentations && drug.presentations.length > 0 && (
                      <div className="border-t pt-4">
                        <h5 className="text-sm font-medium mb-3 flex items-center gap-2">
                          <Pill className="h-4 w-4" />
                          Apresentações ({drug.presentations.length})
                        </h5>
                        <div className="space-y-3">
                          {drug.presentations.map((presentation) => (
                            <div key={presentation.id} className="bg-gray-50 p-4 rounded-lg border">
                              <div className="flex justify-between items-start mb-3">
                                <div>
                                  <span className="font-medium text-base">
                                    {presentation.dosage_form} - {presentation.strength}
                                  </span>
                                  {presentation.is_primary && (
                                    <Badge variant="outline" className="text-xs ml-2 bg-green-50 text-green-700 border-green-300">
                                      Apresentação Principal
                                    </Badge>
                                  )}
                                </div>
                                <div className="flex gap-1">
                                  <Badge
                                    variant={presentation.prescription_category === 'vermelho' ? 'destructive' : 'outline'}
                                    className="text-xs"
                                  >
                                    {presentation.prescription_category}
                                  </Badge>
                                  <Badge variant="secondary" className="text-xs">
                                    {presentation.prescription_type}
                                  </Badge>
                                </div>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                {presentation.therapeutic_classes.length > 0 && (
                                  <div>
                                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Classes Terapêuticas</label>
                                    <div className="flex flex-wrap gap-1 mt-1">
                                      {presentation.therapeutic_classes.map((tc, idx) => (
                                        <Badge key={idx} variant="outline" className="text-xs">
                                          {tc}
                                        </Badge>
                                      ))}
                                    </div>
                                  </div>
                                )}

                                {presentation.administration_routes.length > 0 && (
                                  <div>
                                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Vias de Administração</label>
                                    <div className="flex flex-wrap gap-1 mt-1">
                                      {presentation.administration_routes.map((route, idx) => (
                                        <Badge key={idx} variant="secondary" className="text-xs">
                                          {route}
                                        </Badge>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
