import React, { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export const ConductsDebug = () => {
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testQuery = async () => {
    setLoading(true);
    try {
      // Testar a query exata do código
      const topicId = '502b44df-791e-4dad-b7b1-7d01ccf08099'; // ID do aleitamento-materno
      
      console.log('🔍 Debug - Testando query para topic_id:', topicId);
      
      const { data: optimizedData, error: optimizedError } = await supabase
        .from('pedbook_conducts_optimized')
        .select('*')
        .eq('topic_id', topicId)
        .eq('published', true)
        .maybeSingle();

      console.log('🔍 Debug - Resultado:', { optimizedData, optimizedError });
      
      setResults({
        optimizedData,
        optimizedError,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('❌ Debug - Erro:', error);
      setResults({
        error: error,
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="m-4">
      <CardHeader>
        <CardTitle>Debug - Condutas Query Test</CardTitle>
      </CardHeader>
      <CardContent>
        <Button onClick={testQuery} disabled={loading}>
          {loading ? 'Testando...' : 'Testar Query'}
        </Button>
        
        {results && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <pre className="text-xs overflow-auto">
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
