import React from "react";
import { useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import ConductsTopicListNew from "./ConductsTopicListNew";
import { ConductsSummary } from "./ConductsSummary";
import { Loader2 } from "lucide-react";
import { perfLogger } from "@/utils/performanceLogger";

// Hook customizado para lógica de decisão
const useRouteDecision = (categorySlug?: string, subcategorySlug?: string, topicSlug?: string) => {
  // Log apenas uma vez por mudança de parâmetros
  React.useEffect(() => {
    perfLogger.logEffect('ConductsRouter', [categorySlug, subcategorySlug, topicSlug], 'PARAMS_CHANGED');
  }, [categorySlug, subcategorySlug, topicSlug]);

  // Decisão simples e declarativa
  const decision = React.useMemo(() => {
    // 3 parâmetros = sempre resumo
    if (topicSlug) {
      return { showSummary: true, reason: '3_PARAMS' };
    }

    // 1 parâmetro = sempre lista
    if (!subcategorySlug) {
      return { showSummary: false, reason: '1_PARAM' };
    }

    // 2 parâmetros = precisa verificar se é subcategoria
    return { showSummary: null, reason: '2_PARAMS_CHECK_NEEDED' };
  }, [categorySlug, subcategorySlug, topicSlug]);

  // Query apenas quando necessário (2 parâmetros)
  const { data: topicData, isLoading } = useQuery({
    queryKey: ['conduct-topic-check', categorySlug, subcategorySlug],
    queryFn: async () => {
      if (!categorySlug || !subcategorySlug) return null;
      
      perfLogger.logRequest('ConductsRouter', `topic-check-${categorySlug}-${subcategorySlug}`);
      
      // Primeiro buscar categoria
      const { data: categoryData, error: categoryError } = await supabase
        .from('pedbook_conducts_categories')
        .select('id, name')
        .eq('slug', categorySlug)
        .single();

      if (categoryError || !categoryData) {
        throw new Error('Category not found');
      }

      // Depois buscar tópico
      const { data: topicData, error: topicError } = await supabase
        .from('pedbook_conducts_topics')
        .select('id, name, is_subcategory')
        .eq('category_id', categoryData.id)
        .eq('slug', subcategorySlug)
        .maybeSingle();

      return topicData;
    },
    enabled: decision.showSummary === null, // Só executa quando precisa verificar
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
  });

  // Decisão final
  const finalDecision = React.useMemo(() => {
    if (decision.showSummary !== null) {
      return decision.showSummary;
    }

    // Se ainda está carregando, retorna null (loading)
    if (isLoading) {
      return null;
    }

    // Se não encontrou OU se is_subcategory é false/null = resumo
    // Se encontrou E is_subcategory é true = lista
    return !topicData?.is_subcategory;
  }, [decision.showSummary, isLoading, topicData]);

  return {
    showSummary: finalDecision,
    isLoading,
    reason: decision.reason
  };
};

// Componente principal simplificado
const ConductsRouterNew = React.memo(() => {
  const { categorySlug, subcategorySlug, topicSlug } = useParams();
  const { showSummary, isLoading, reason } = useRouteDecision(categorySlug, subcategorySlug, topicSlug);

  // Log de renderização simples
  perfLogger.logRender('ConductsRouter', { 
    categorySlug, 
    subcategorySlug, 
    topicSlug, 
    showSummary, 
    isLoading,
    reason 
  });

  // Loading state
  if (isLoading || showSummary === null) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
      </div>
    );
  }

  // Renderização condicional simples
  return showSummary ? <ConductsSummary /> : <ConductsTopicListNew />;
});

ConductsRouterNew.displayName = 'ConductsRouterNew';

export default ConductsRouterNew;
