import { useState } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { useToast } from '@/components/ui/use-toast';

interface FormatContentResponse {
  formattedContent: string;
  success: boolean;
  error?: string;
}

export const useContentFormatter = () => {
  const [isFormatting, setIsFormatting] = useState(false);
  const supabase = useSupabaseClient();
  const { toast } = useToast();

  const formatContent = async (content: string): Promise<string | null> => {
    if (!content || content.trim().length === 0) {
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Não há conteúdo para formatar."
      });
      return null;
    }

    if (content.trim().length < 10) {
      toast({
        variant: "destructive",
        title: "Erro",
        description: "O conteúdo deve ter pelo menos 10 caracteres para ser formatado."
      });
      return null;
    }

    setIsFormatting(true);

    try {
      console.log('🚀 Calling format-content function with content length:', content.trim().length);

      const { data, error } = await supabase.functions.invoke('format-content', {
        body: { content: content.trim() }
      });

      console.log('📥 Function response:', { data, error });

      if (error) {
        console.error('❌ Supabase function error:', error);
        throw new Error(error.message || 'Erro ao chamar função de formatação');
      }

      if (!data || !data.success) {
        console.error('❌ Function returned error:', data);
        throw new Error(data?.error || data?.details || 'Erro desconhecido na formatação');
      }

      toast({
        title: "Sucesso!",
        description: "Conteúdo formatado com sucesso pela IA.",
        variant: "default"
      });

      return data.formattedContent;

    } catch (error) {
      console.error('Error formatting content:', error);

      let errorMessage = 'Erro inesperado ao formatar conteúdo';

      if (error instanceof Error) {
        // Tratamento específico para quota excedida
        if (error.message.includes('quota') || error.message.includes('429') || error.message.includes('Too Many Requests')) {
          errorMessage = 'Limite diário da IA atingido. Tente novamente amanhã ou considere fazer upgrade do plano.';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'Erro de conexão. Verifique sua internet e tente novamente.';
        } else if (error.message.includes('timeout')) {
          errorMessage = 'Tempo limite excedido. Tente novamente com um texto menor.';
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        variant: "destructive",
        title: "Erro na formatação",
        description: errorMessage
      });

      return null;
    } finally {
      setIsFormatting(false);
    }
  };

  return {
    formatContent,
    isFormatting
  };
};
