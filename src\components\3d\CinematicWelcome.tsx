import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Play, ArrowRight, Sparkles, GraduationCap, BookOpen, Target, X } from "lucide-react";

interface CinematicWelcomeProps {
  onComplete: () => void;
  onStartStudy: () => void;
}

type CinematicStage = 'welcome' | 'features' | 'cta';

export const CinematicWelcome: React.FC<CinematicWelcomeProps> = ({
  onComplete,
  onStartStudy
}) => {
  const [stage, setStage] = useState<CinematicStage>('welcome');

  useEffect(() => {
    const sequence = async () => {
      if (stage === 'welcome') {
        await new Promise(resolve => setTimeout(resolve, 3000));
        setStage('features');
        await new Promise(resolve => setTimeout(resolve, 4000));
        setStage('cta');
      }
    };

    sequence();
  }, [stage]);

  const handleStartStudy = () => {
    onStartStudy();
  };

  const handleSkip = () => {
    onComplete();
  };

  const features = [
    {
      icon: GraduationCap,
      title: "Questões de Pediatria",
      description: "Milhares de questões especializadas"
    },
    {
      icon: Target,
      title: "Filtros Inteligentes",
      description: "Estude por especialidade e tema"
    },
    {
      icon: BookOpen,
      title: "Resultados Detalhados",
      description: "Acompanhe seu progresso"
    }
  ];

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, #3b82f6 0%, transparent 50%), 
                           radial-gradient(circle at 75% 75%, #8b5cf6 0%, transparent 50%)`,
        }} />
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full opacity-60"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, -100],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Skip Button */}
      <motion.button
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        onClick={handleSkip}
        className="absolute top-6 right-6 z-50 bg-white/10 backdrop-blur-sm rounded-full p-3 text-white hover:bg-white/20 transition-colors"
      >
        <X className="w-5 h-5" />
      </motion.button>

      {/* Stage Content */}
      <AnimatePresence mode="wait">
        {stage === 'welcome' && (
          <motion.div
            key="welcome"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 1.1 }}
            className="flex items-center justify-center h-full p-8"
          >
            <div className="text-center text-white max-w-2xl">
              <motion.div
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.8 }}
              >
                <Sparkles className="w-16 h-16 mx-auto mb-6 text-yellow-400" />
                <h1 className="text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 text-transparent bg-clip-text">
                  PedBook
                </h1>
                <p className="text-xl md:text-2xl text-gray-300 mb-8">
                  Prévia gratuita do MedEvo focada em pediatria
                </p>
              </motion.div>
            </div>
          </motion.div>
        )}

        {stage === 'features' && (
          <motion.div
            key="features"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            className="flex items-center justify-center h-full p-4 sm:p-6 lg:p-8"
          >
            <div className="text-center text-white max-w-4xl px-4">
              <motion.h2
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-2xl sm:text-3xl md:text-4xl font-bold mb-8 sm:mb-12"
              >
                Recursos da Plataforma
              </motion.h2>

              {/* 📱 MOBILE OTIMIZADO: Grid responsivo que evita cortes */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ y: 50, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.4 + index * 0.2 }}
                    className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-white/20 hover:bg-white/15 transition-colors"
                  >
                    <feature.icon className="w-10 h-10 sm:w-12 sm:h-12 mx-auto mb-3 sm:mb-4 text-blue-400" />
                    <h3 className="text-lg sm:text-xl font-semibold mb-2">{feature.title}</h3>
                    <p className="text-gray-300 text-sm sm:text-base leading-relaxed">{feature.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        )}

        {stage === 'cta' && (
          <motion.div
            key="cta"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="flex items-center justify-center h-full p-4 sm:p-6 lg:p-8"
          >
            <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-lg rounded-3xl p-6 sm:p-8 md:p-12 max-w-2xl mx-4 text-center shadow-2xl border border-white/20 dark:border-gray-700/30">
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 dark:text-gray-200 mb-4 sm:mb-6">
                  🚀 Pronto para começar?
                </h2>
                <p className="text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 mb-6 sm:mb-8 leading-relaxed">
                  Teste como é estudar no MedEvo com questões de pediatria!
                </p>

                <div className="space-y-3 sm:space-y-4">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleStartStudy}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-2xl text-base sm:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2 sm:gap-3"
                  >
                    <Play className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" />
                    <span className="truncate">Testar Prévia</span>
                    <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" />
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleSkip}
                    className="w-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 px-6 sm:px-8 py-2.5 sm:py-3 rounded-2xl text-sm sm:text-base font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300"
                  >
                    Explorar Mais Tarde
                  </motion.button>
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
