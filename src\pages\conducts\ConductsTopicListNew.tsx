import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ChevronLeft, ChevronRight, Search, Loader2, Folder, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import { TopicCard } from "@/components/conducts/TopicCard";
import { calculateLevenshteinDistance, removeMarkdown } from "@/lib/utils";

import { perfLogger } from "@/utils/performanceLogger";

interface Topic {
  id: string;
  name: string;
  slug: string;
  category_name?: string;
  is_subcategory?: boolean;
  parent_id?: string;
  summary_count?: number;
}

// Hook para buscar categoria
const useCategoryData = (categorySlug?: string) => {
  return useQuery({
    queryKey: ['conduct-category', categorySlug],
    queryFn: async () => {
      if (!categorySlug) return null;
      
      perfLogger.logRequest('ConductsTopicList', `category-${categorySlug}`);
      const { data, error } = await supabase
        .from('pedbook_conducts_categories')
        .select('id, name')
        .eq('slug', categorySlug)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!categorySlug,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Hook para buscar subcategoria
const useSubcategoryData = (categoryId?: string, subcategorySlug?: string) => {
  return useQuery({
    queryKey: ['conduct-subcategory', categoryId, subcategorySlug],
    queryFn: async () => {
      if (!categoryId || !subcategorySlug) return null;
      
      perfLogger.logRequest('ConductsTopicList', `subcategory-${categoryId}-${subcategorySlug}`);
      const { data, error } = await supabase
        .from('pedbook_conducts_topics')
        .select('id, name, is_subcategory')
        .eq('category_id', categoryId)
        .eq('slug', subcategorySlug)
        .eq('is_subcategory', true)
        .maybeSingle();

      if (error) throw error;
      return data;
    },
    enabled: !!categoryId && !!subcategorySlug,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Hook para buscar tópicos
const useTopicsData = (categorySlug?: string, subcategoryId?: string) => {
  return useQuery({
    queryKey: ['conduct-topics', categorySlug, subcategoryId],
    queryFn: async () => {
      if (!categorySlug) return [];
      
      perfLogger.logRequest('ConductsTopicList', `topics-${categorySlug}-${subcategoryId || 'direct'}`);
      
      let query;
      if (subcategoryId) {
        // Buscar tópicos filhos da subcategoria
        query = supabase
          .from('v_conducts_topics')
          .select('id, name, slug, category_name, is_subcategory, parent_id')
          .eq('parent_id', subcategoryId);
      } else {
        // Buscar tópicos diretos da categoria
        query = supabase
          .from('v_conducts_topics')
          .select('id, name, slug, category_name, is_subcategory, parent_id')
          .eq('category_slug', categorySlug)
          .is('parent_id', null);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as Topic[];
    },
    enabled: !!categorySlug,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Hook para buscar contagens de summaries (OTIMIZADO - 1 query para todos)
const useSummaryCounts = (topicIds: string[]) => {
  return useQuery({
    queryKey: ['conduct-summary-counts', topicIds.sort().join(',')],
    queryFn: async () => {
      if (topicIds.length === 0) return {};
      
      perfLogger.logRequest('ConductsTopicList', `summary-counts-batch-${topicIds.length}`);
      
      // UMA ÚNICA QUERY para todos os tópicos
      const [summariesResult, optimizedResult] = await Promise.all([
        supabase
          .from('pedbook_conducts_summaries')
          .select('topic_id')
          .in('topic_id', topicIds)
          .eq('published', true),
        supabase
          .from('pedbook_conducts_optimized')
          .select('topic_id')
          .in('topic_id', topicIds)
          .eq('published', true)
      ]);

      // Contar por topic_id
      const counts: Record<string, number> = {};
      
      summariesResult.data?.forEach(item => {
        counts[item.topic_id] = (counts[item.topic_id] || 0) + 1;
      });
      
      optimizedResult.data?.forEach(item => {
        counts[item.topic_id] = (counts[item.topic_id] || 0) + 1;
      });

      return counts;
    },
    enabled: topicIds.length > 0,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

export const ConductsTopicListNew = React.memo(() => {
  const { categorySlug, subcategorySlug } = useParams();
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredTopics, setFilteredTopics] = useState<Topic[]>([]);

  const navigate = useNavigate();
  const { toast } = useToast();

  // Queries em cascata otimizada
  const { data: categoryData, isLoading: isLoadingCategory } = useCategoryData(categorySlug);
  const { data: subcategoryData, isLoading: isLoadingSubcategory } = useSubcategoryData(categoryData?.id, subcategorySlug);
  const { data: topicsData = [], isLoading: isLoadingTopics } = useTopicsData(categorySlug, subcategoryData?.id);
  
  // Extrair IDs dos tópicos para buscar contagens
  const topicIds = topicsData.map(topic => topic.id);
  const { data: summaryCounts = {}, isLoading: isLoadingCounts } = useSummaryCounts(topicIds);

  // Combinar tópicos com contagens e ordenar alfabeticamente
  const enrichedTopics = React.useMemo(() => {
    const enriched = topicsData.map(topic => ({
      ...topic,
      summary_count: summaryCounts[topic.id] || 0
    }));

    // 🔧 CORREÇÃO: Ordenar alfabeticamente (A-Z)
    return enriched.sort((a, b) => a.name.localeCompare(b.name, 'pt-BR'));
  }, [topicsData, summaryCounts]);

  // Loading state
  const loading = isLoadingCategory || isLoadingSubcategory || isLoadingTopics || isLoadingCounts;

  // Log de renderização
  perfLogger.logRender('ConductsTopicList', { 
    categorySlug, 
    subcategorySlug, 
    topicsCount: enrichedTopics.length,
    loading 
  });

  // Filtrar tópicos baseado na busca
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredTopics(enrichedTopics);
      return;
    }

    const filtered = enrichedTopics.filter(topic => {
      const normalizedSearch = searchTerm.toLowerCase();
      const normalizedName = topic.name.toLowerCase();
      
      return normalizedName.includes(normalizedSearch) ||
             calculateLevenshteinDistance(normalizedName, normalizedSearch) <= 2;
    });

    setFilteredTopics(filtered);
  }, [searchTerm, enrichedTopics]);

  // Função para normalizar texto
  const normalizeText = (text: string) => {
    return text
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex flex-col">
      <Header />

      {/* 🎨 LAYOUT REFORMULADO: Usando mais espaço da tela */}
      <main className="flex-1 container mx-auto px-4 lg:px-6 xl:px-8 py-6 md:py-8">
        <div className="max-w-7xl mx-auto">

          {/* 🎨 HEADER COMPACTO: Botão e breadcrumb na mesma linha */}
          <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl border border-white/20 dark:border-slate-700/50 p-4 mb-6 shadow-lg">

            {/* Navegação e Breadcrumb em uma linha */}
            <div className="flex items-center gap-3 mb-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (subcategorySlug) {
                    navigate(`/condutas-e-manejos/${categorySlug}`);
                  } else {
                    navigate('/condutas-e-manejos');
                  }
                }}
                className="flex items-center gap-1 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 px-2 py-1 rounded-lg transition-all text-sm"
              >
                <ChevronLeft className="w-4 h-4" />
                <span className="hidden sm:inline">Voltar</span>
              </Button>

              {/* Breadcrumb compacto */}
              <div className="flex items-center gap-1 text-gray-600 dark:text-gray-300 min-w-0 flex-1">
                <div className="flex items-center gap-1 px-2 py-1 bg-blue-50 dark:bg-blue-900/30 rounded-md min-w-0">
                  <span className="text-sm font-medium text-blue-700 dark:text-blue-300 truncate">
                    {categoryData?.name}
                  </span>
                  {subcategoryData && (
                    <>
                      <ChevronRight className="w-3 h-3 text-blue-400 flex-shrink-0" />
                      <span className="text-sm font-medium text-blue-600 dark:text-blue-400 truncate">
                        {subcategoryData.name}
                      </span>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* 🎨 BUSCA MELHORADA: Mais destaque */}
            <div className="relative mt-6">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="🔍 Buscar tópicos e subcategorias..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-12 pr-4 py-3 text-base bg-white dark:bg-slate-700 border-2 border-blue-100 dark:border-blue-800 focus:border-blue-300 dark:focus:border-blue-600 rounded-xl shadow-sm"
              />
            </div>
          </div>

          {/* Loading */}
          {loading && (
            <div className="flex flex-col items-center justify-center py-16">
              <Loader2 className="w-12 h-12 animate-spin text-blue-500 mb-4" />
              <p className="text-gray-600 dark:text-gray-400">Carregando tópicos...</p>
            </div>
          )}

          {/* 🎨 LISTA DE TÓPICOS REFORMULADA: Grid responsivo e melhor aproveitamento */}
          {!loading && (
            <>
              {filteredTopics.length > 0 ? (
                <div className="space-y-6">

                  {/* Separar subcategorias dos tópicos para melhor organização */}
                  {(() => {
                    const subcategories = filteredTopics.filter(topic => topic.is_subcategory);
                    const regularTopics = filteredTopics.filter(topic => !topic.is_subcategory);

                    return (
                      <>
                        {/* Subcategorias em destaque */}
                        {subcategories.length > 0 && (
                          <div className="space-y-4">
                            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                              <Folder className="w-5 h-5 text-blue-500" />
                              Subcategorias
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                              {subcategories.map((topic) => (
                                <TopicCard
                                  key={topic.id}
                                  topic={topic}
                                  categorySlug={categorySlug || ''}
                                  subcategorySlug={subcategorySlug}
                                  searchTerm={searchTerm}
                                />
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Tópicos regulares */}
                        {regularTopics.length > 0 && (
                          <div className="space-y-4">
                            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                              <BookOpen className="w-5 h-5 text-green-500" />
                              Tópicos {subcategories.length > 0 ? `(${regularTopics.length})` : ''}
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4">
                              {regularTopics.map((topic) => (
                                <TopicCard
                                  key={topic.id}
                                  topic={topic}
                                  categorySlug={categorySlug || ''}
                                  subcategorySlug={subcategorySlug}
                                  searchTerm={searchTerm}
                                />
                              ))}
                            </div>
                          </div>
                        )}
                      </>
                    );
                  })()}
                </div>
              ) : (
                /* Estado vazio melhorado */
                <div className="text-center py-16">
                  <div className="max-w-md mx-auto">
                    <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-full flex items-center justify-center">
                      <Search className="w-12 h-12 text-blue-500 dark:text-blue-400" />
                    </div>
                    <h3 className="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-3">
                      Nenhum tópico encontrado
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400 mb-6">
                      {searchTerm
                        ? `Não encontramos resultados para "${searchTerm}". Tente ajustar sua pesquisa.`
                        : 'Não há tópicos disponíveis nesta categoria no momento.'
                      }
                    </p>
                    {searchTerm && (
                      <Button
                        variant="outline"
                        onClick={() => setSearchTerm('')}
                        className="mx-auto"
                      >
                        Limpar busca
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
});

ConductsTopicListNew.displayName = 'ConductsTopicListNew';

export default ConductsTopicListNew;
