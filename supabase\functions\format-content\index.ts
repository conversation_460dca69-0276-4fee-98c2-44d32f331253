import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

const GEMINI_API_KEY = Deno.env.get('GEMINI_API_KEY')

serve(async (req) => {
  console.log('🚀 Format-content function called - VERSION 2.0 (NO THINKING CONFIG)')

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('✅ CORS preflight request handled')
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('📝 Parsing request body...')
    const body = await req.json()
    console.log('📦 Request body:', { hasContent: !!body.content, contentLength: body.content?.length })

    const { content } = body

    if (!content || typeof content !== 'string') {
      console.log('❌ Invalid content provided')
      return new Response(
        JSON.stringify({ error: 'Content is required and must be a string' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (!GEMINI_API_KEY) {
      console.log('❌ Gemini API key not configured')
      return new Response(
        JSON.stringify({ error: 'Gemini API key not configured' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('✅ API key found, content valid. Preparing Gemini request...')

    const prompt = `Você é um especialista em formatação de conteúdo médico em Markdown. Sua única função é formatar o texto fornecido seguindo estas regras RIGOROSAMENTE:

REGRAS OBRIGATÓRIAS:
1. NÃO REMOVA nenhum conteúdo existente
2. NÃO ADICIONE informações novas
3. NÃO ALTERE o significado médico
4. APENAS formate usando Markdown

FORMATAÇÃO DESEJADA:
- Use ## para títulos principais (ex: ## 1. Definição e Contexto Clínico)
- Use ### para subtítulos (ex: ### Escore de Apgar)
- Use **texto** para destacar termos importantes
- Use * para listas com bullets
- Use | para tabelas quando apropriado
- Use quebras de linha para melhor legibilidade

EXEMPLO DE FORMATAÇÃO:
## 1. Definição e Contexto Clínico
O aleitamento materno é considerado **padrão ouro** da nutrição infantil...

**Fases do leite materno:**
* **Colostro (0-5 dias):** Amarelado, rico em imunoglobulinas
* **Leite de transição (5-15 dias):** Aumento progressivo de volume

| **Parâmetro** | **0 pontos** | **1 ponto** | **2 pontos** |
|---------------|--------------|-------------|--------------|
| FC            | Ausente      | <100 bpm    | >100 bpm     |

TEXTO PARA FORMATAR:
${content}

RETORNE APENAS O TEXTO FORMATADO, SEM EXPLICAÇÕES ADICIONAIS:`

    console.log('🤖 Calling Gemini API...')

    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: prompt
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.1,
        topK: 1,
        topP: 0.8,
        maxOutputTokens: 8192,
      }
    }

    console.log('📤 Request body prepared (VERSION 2 - NO THINKING CONFIG):', {
      hasThinkingConfig: 'thinkingConfig' in requestBody,
      keys: Object.keys(requestBody),
      generationConfigKeys: Object.keys(requestBody.generationConfig)
    })

    const requestBodyString = JSON.stringify(requestBody);
    console.log('📋 Full request body JSON (first 500 chars):', requestBodyString.substring(0, 500));
    console.log('🔍 Contains thinkingConfig?', requestBodyString.includes('thinkingConfig'));

    const geminiResponse = await fetch(
      "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-goog-api-key": GEMINI_API_KEY,
        },
        body: requestBodyString,
      }
    )

    console.log('📥 Gemini response received:', {
      status: geminiResponse.status,
      statusText: geminiResponse.statusText,
      ok: geminiResponse.ok
    })

    if (!geminiResponse.ok) {
      const errorText = await geminiResponse.text()
      console.error('❌ Gemini API error:', {
        status: geminiResponse.status,
        statusText: geminiResponse.statusText,
        errorText: errorText
      })
      return new Response(
        JSON.stringify({
          error: 'Failed to format content',
          details: `Gemini API returned ${geminiResponse.status}: ${errorText}`
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('🔄 Parsing Gemini response...')
    const geminiData = await geminiResponse.json()

    console.log('📊 Gemini response structure:', {
      hasCandidates: !!geminiData.candidates,
      candidatesLength: geminiData.candidates?.length,
      hasFirstCandidate: !!geminiData.candidates?.[0],
      hasContent: !!geminiData.candidates?.[0]?.content,
      hasParts: !!geminiData.candidates?.[0]?.content?.parts,
      partsLength: geminiData.candidates?.[0]?.content?.parts?.length
    })

    if (!geminiData.candidates || !geminiData.candidates[0] || !geminiData.candidates[0].content) {
      console.error('❌ Invalid Gemini response structure:', geminiData)
      return new Response(
        JSON.stringify({
          error: 'Invalid response from Gemini API',
          details: 'Missing candidates or content in response'
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    const formattedContent = geminiData.candidates[0].content.parts[0].text
    console.log('✅ Content formatted successfully:', {
      originalLength: content.length,
      formattedLength: formattedContent.length
    })

    return new Response(
      JSON.stringify({
        formattedContent: formattedContent.trim(),
        success: true
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('❌ Error in format-content function:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    })
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: error.message
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
