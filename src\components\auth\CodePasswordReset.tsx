import { useState, useEffect, useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Mail, Lock, CheckCircle, Loader2 } from "lucide-react";
import { useNotification } from "@/context/NotificationContext";
import { supabase } from "@/integrations/supabase/client";

// Schema para email
const emailSchema = z.object({
  email: z.string().email("Email inválido"),
});

// Schema para código + senha
const resetSchema = z.object({
  code: z.string().length(6, "O código deve ter 6 dígitos"),
  password: z.string()
    .min(8, "A senha deve ter pelo menos 8 caracteres")
    .regex(/^(?=.*[a-zA-Z])(?=.*\d)/, "A senha deve conter pelo menos uma letra e um número"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "As senhas não coincidem",
  path: ["confirmPassword"],
});

type Step = 'email' | 'code' | 'success';

interface CodePasswordResetProps {
  onBack: () => void;
}

export function CodePasswordReset({ onBack }: CodePasswordResetProps) {
  const [step, setStep] = useState<Step>('email');
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [codeDigits, setCodeDigits] = useState(['', '', '', '', '', '']);
  const { showNotification } = useNotification();

  // Refs para os inputs do código
  const codeInputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const emailForm = useForm<z.infer<typeof emailSchema>>({
    resolver: zodResolver(emailSchema),
    defaultValues: { email: "" },
  });

  const resetForm = useForm<z.infer<typeof resetSchema>>({
    resolver: zodResolver(resetSchema),
    defaultValues: { code: "", password: "", confirmPassword: "" },
  });

  // Reset form quando mudar de step
  useEffect(() => {
    if (step === 'code') {
      // Force reset with explicit values
      setCodeDigits(['', '', '', '', '', '']);
      resetForm.setValue('code', '');
      resetForm.setValue('password', '');
      resetForm.setValue('confirmPassword', '');
      resetForm.clearErrors();
    }
  }, [step, resetForm]);

  // Enviar código por email
  const onSendCode = async (values: z.infer<typeof emailSchema>) => {
    try {
      setIsLoading(true);
      setError('');

      // Enviar código de recuperação por email

      // Usar resetPasswordForEmail que agora enviará o código
      const { data, error } = await supabase.auth.resetPasswordForEmail(values.email, {
        redirectTo: `${window.location.origin}/reset-password`
      });

      if (error) {
        throw error;
      }
      setEmail(values.email);
      setStep('code');

    } catch (error: any) {
      setError(error.message || 'Erro ao enviar código');
    } finally {
      setIsLoading(false);
    }
  };

  // Validar código e atualizar senha
  const onResetPassword = async (values: z.infer<typeof resetSchema>) => {
    try {
      setIsLoading(true);
      setError('');

      // Validar código de recuperação

      // Usar verifyOtp para validar o código
      const { data, error } = await supabase.auth.verifyOtp({
        email: email,
        token: values.code,
        type: 'recovery'
      });

      if (error) {
        throw new Error('Código inválido ou expirado');
      }

      if (!data.user) {
        throw new Error('Erro na validação do código');
      }

      // Código válido, atualizar senha

      // Atualizar senha
      const { error: updateError } = await supabase.auth.updateUser({
        password: values.password
      });

      if (updateError) {
        throw updateError;
      }
      setStep('success');

      showNotification({
        title: "Senha atualizada!",
        description: "Sua senha foi alterada com sucesso. Recarregando página...",
        type: "success",
        buttonText: "Continuar"
      });

      // Forçar refresh da página para limpar contexto de autenticação
      setTimeout(() => {
        window.location.reload();
      }, 1500);

    } catch (error: any) {
      console.error('Erro no reset de senha:', error);

      // CRÍTICO: Fazer logout imediatamente se houver qualquer erro
      // O usuário foi autenticado na verificação do código, mas a senha falhou
      // Não devemos manter o usuário logado se a operação não foi concluída
      try {
        await supabase.auth.signOut();
        console.log('🔒 Logout realizado após erro na troca de senha');
      } catch (logoutError) {
        console.error('Erro ao fazer logout:', logoutError);
      }

      // IMPORTANTE: Qualquer erro invalida o token no Supabase
      // Precisamos sempre solicitar novo código após qualquer erro

      if (error.message?.includes('should be different') || error.message?.includes('same password')) {
        setError('⚠️ A nova senha deve ser diferente da senha atual. Você foi deslogado por segurança. Solicite um novo código para tentar novamente.');
        // Auto-redirecionar após 3 segundos
        setTimeout(() => {
          setStep('email');
          setCodeDigits(['', '', '', '', '', '']);
          resetForm.reset();
          setError('');
        }, 3000);
      } else if (error.message?.includes('weak') || error.message?.includes('too short')) {
        setError('⚠️ Senha muito fraca. Você foi deslogado por segurança. Solicite um novo código para tentar novamente.');
        // Auto-redirecionar após 3 segundos
        setTimeout(() => {
          setStep('email');
          setCodeDigits(['', '', '', '', '', '']);
          resetForm.reset();
          setError('');
        }, 3000);
      } else if (error.message?.includes('expired') || error.message?.includes('invalid') || error.message?.includes('Forbidden')) {
        setError('🔒 Código expirado ou inválido. Solicite um novo código.');
        // Auto-redirecionar após 2 segundos
        setTimeout(() => {
          setStep('email');
          setCodeDigits(['', '', '', '', '', '']);
          resetForm.reset();
          setError('');
        }, 2000);
      } else {
        setError('❌ Erro inesperado. Você foi deslogado por segurança. Solicite um novo código.');
        // Auto-redirecionar após 3 segundos
        setTimeout(() => {
          setStep('email');
          setCodeDigits(['', '', '', '', '', '']);
          resetForm.reset();
          setError('');
        }, 3000);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Voltar para solicitar novo código
  const goBack = () => {
    setStep('email');
    setError('');
    setCodeDigits(['', '', '', '', '', '']);
    emailForm.reset();
    resetForm.reset();
  };

  // Etapa 1: Solicitar código
  if (step === 'email') {
    return (
      <Card className="w-full">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="p-1 h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
              <Mail className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <CardTitle className="text-center">Recuperar Senha</CardTitle>
          <CardDescription className="text-center">
            Digite seu email para receber um código de recuperação
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...emailForm}>
            <form onSubmit={emailForm.handleSubmit(onSendCode)} className="space-y-4">
              <FormField
                control={emailForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="<EMAIL>"
                        type="email"
                        {...field}
                        className="bg-gray-50 border-gray-200 focus:border-primary"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {error && (
                <div className="text-sm text-red-600 bg-red-50 p-3 rounded-lg">
                  {error}
                </div>
              )}

              <Button
                type="submit"
                className="w-full bg-primary text-white hover:bg-primary/90"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Enviando código...
                  </>
                ) : (
                  <>
                    <Mail className="mr-2 h-4 w-4" />
                    Enviar código
                  </>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    );
  }

  // Etapa 2: Inserir código e nova senha
  if (step === 'code') {
    return (
      <Card className="w-full overflow-hidden">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={goBack}
              className="p-1 h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
              <Lock className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <CardTitle className="text-center">Digite o Código</CardTitle>
          <CardDescription className="text-center">
            Código enviado para {email}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...resetForm}>
            <form onSubmit={resetForm.handleSubmit(onResetPassword)} className="space-y-6">
              <div className="space-y-3">
                <label className="text-sm font-medium text-center block">Código de verificação</label>
                <div className="flex gap-1 sm:gap-2 justify-center flex-wrap max-w-full">
                  {codeDigits.map((digit, index) => (
                    <Input
                      key={index}
                      ref={(el) => (codeInputRefs.current[index] = el)}
                      type="text"
                      inputMode="numeric"
                      maxLength={1}
                      value={digit}
                      onChange={(e) => {
                        const value = e.target.value.replace(/\D/g, '');
                        if (value.length <= 1) {
                          const newDigits = [...codeDigits];
                          newDigits[index] = value;
                          setCodeDigits(newDigits);

                          // Update form field
                          resetForm.setValue('code', newDigits.join(''));

                          // Auto-focus next input
                          if (value && index < 5) {
                            codeInputRefs.current[index + 1]?.focus();
                          }
                        }
                      }}
                      onKeyDown={(e) => {
                        // Handle backspace
                        if (e.key === 'Backspace' && !digit && index > 0) {
                          codeInputRefs.current[index - 1]?.focus();
                        }
                      }}
                      className="w-10 h-10 sm:w-12 sm:h-12 text-center text-lg sm:text-xl font-mono bg-gray-50 border-gray-200 focus:border-primary flex-shrink-0"
                    />
                  ))}
                </div>
              </div>

              <FormField
                control={resetForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nova senha</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Nova senha (diferente da atual)"
                        {...field}
                        className="bg-gray-50 border-gray-200 focus:border-primary"
                      />
                    </FormControl>
                    <FormDescription className="text-xs text-gray-500">
                      Mínimo 8 caracteres com letras e números. <strong>Deve ser diferente da senha atual.</strong>
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={resetForm.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirmar senha</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Confirme sua nova senha"
                        {...field}
                        className="bg-gray-50 border-gray-200 focus:border-primary"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {error && (
                <div className="text-sm text-red-600 bg-red-50 p-3 rounded-lg">
                  {error}
                </div>
              )}

              <Button
                type="submit"
                className="w-full bg-primary text-white hover:bg-primary/90"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Atualizando senha...
                  </>
                ) : (
                  <>
                    <Lock className="mr-2 h-4 w-4" />
                    Atualizar senha
                  </>
                )}
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setStep('email');
                  setCodeDigits(['', '', '', '', '', '']);
                  setError('');
                  resetForm.reset();
                }}
                className="w-full mt-2"
              >
                <Mail className="mr-2 h-4 w-4" />
                Solicitar novo código
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    );
  }

  // Etapa 3: Sucesso
  return (
    <Card className="w-full">
      <CardContent className="pt-6 pb-8 px-6 flex flex-col items-center text-center">
        <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-6">
          <CheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Senha Atualizada!</h2>
        <p className="text-gray-600 mb-6">
          Sua senha foi alterada com sucesso. Você pode fazer login agora.
        </p>
        <Button
          variant="duolingo"
          className="w-full"
          onClick={onBack}
        >
          Fazer login
        </Button>
      </CardContent>
    </Card>
  );
}
