import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import {
  Spa<PERSON>les,
  Loader2,
  CheckCircle,
  XCircle,
  Edit3,
  Save,
  RotateCcw,
  AlertTriangle,
  Info,
  BrainCircuit,
  Zap
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface ActiveIngredient {
  id: string;
  name: string;
  dcb_code: string | null;
  cas_number: string | null;
}

interface DrugData {
  id: string;
  brand_name: string;
  descricao: string | null;
  fabricante: string | null;
  composicao: string | null;
  pregnancy_info: string | null;
  breastfeeding_info: string | null;
  patient_instructions: string[] | null;
  adult_use: boolean | null;
  pediatric_use: boolean | null;
  is_controlled: boolean;
  is_high_cost: boolean;
  status: string;
  tipo: string | null;
  titularidade: string | null;
  eans: string[] | null;
  is_association: boolean | null;
  therapeutic_classes?: string[]; // Classes terapêuticas da nova estrutura
}

interface AIAnalysis {
  field: string;
  fieldLabel: string;
  currentValue: string;
  suggestedValue: string;
  reasoning: string;
  confidence: 'high' | 'medium' | 'low';
  status: 'pending' | 'approved' | 'rejected';
}

interface AIAnalysisPanelProps {
  ingredient: ActiveIngredient;
  relatedDrugs: DrugData[];
  onDataUpdated: () => void;
}

export const AIAnalysisPanel: React.FC<AIAnalysisPanelProps> = ({
  ingredient,
  relatedDrugs,
  onDataUpdated
}) => {
  const { toast } = useToast();
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analyses, setAnalyses] = useState<AIAnalysis[]>([]);
  const [selectedDrug, setSelectedDrug] = useState<DrugData | null>(null);
  const [editingAnalysis, setEditingAnalysis] = useState<string | null>(null);
  const [editedValue, setEditedValue] = useState("");

  const analyzeWithAI = async (drug: DrugData) => {
    try {
      setIsAnalyzing(true);
      setSelectedDrug(drug);
      
      // Simular análise de IA (aqui você integraria com o Dr. Will ou outro serviço de IA)
      const mockAnalyses: AIAnalysis[] = [];

      // Analisar descrição
      if (!drug.descricao || drug.descricao.trim() === '' || drug.descricao === drug.brand_name) {
        mockAnalyses.push({
          field: 'descricao',
          fieldLabel: 'Descrição',
          currentValue: drug.descricao || '',
          suggestedValue: `${ingredient.name} é um medicamento utilizado para [indicação terapêutica]. Pertence à classe ${drug.drug_class || '[classe terapêutica]'} e é indicado para o tratamento de [condições específicas].`,
          reasoning: 'A descrição atual está vazia ou muito básica. Uma descrição mais detalhada ajudaria profissionais de saúde a entender melhor o medicamento.',
          confidence: 'high',
          status: 'pending'
        });
      }

      // Analisar informações de gravidez
      if (!drug.pregnancy_info || drug.pregnancy_info === 'nd' || drug.pregnancy_info.trim() === '') {
        mockAnalyses.push({
          field: 'pregnancy_info',
          fieldLabel: 'Informações sobre Gravidez',
          currentValue: drug.pregnancy_info || '',
          suggestedValue: `<b>Categoria de risco na gravidez: [A/B/C/D/X]</b>\n\nEste medicamento deve ser usado durante a gravidez somente quando o benefício justificar o risco potencial. Consulte sempre um médico antes de usar durante a gestação.`,
          reasoning: 'Informações sobre segurança na gravidez são essenciais para prescrição segura.',
          confidence: 'medium',
          status: 'pending'
        });
      }

      // Analisar informações de amamentação
      if (!drug.breastfeeding_info || drug.breastfeeding_info === 'nd' || drug.breastfeeding_info.trim() === '') {
        mockAnalyses.push({
          field: 'breastfeeding_info',
          fieldLabel: 'Informações sobre Amamentação',
          currentValue: drug.breastfeeding_info || '',
          suggestedValue: `<b>[Verde/Amarelo/Vermelho]: [Compatível/Uso criterioso/Contraindicado] durante a amamentação</b>\n\nEste medicamento [pode ser usado com segurança/requer monitoramento/deve ser evitado] durante a amamentação. Consulte um médico para orientações específicas.`,
          reasoning: 'Informações sobre compatibilidade com amamentação são cruciais para mães lactantes.',
          confidence: 'medium',
          status: 'pending'
        });
      }

      // Analisar instruções ao paciente
      if (!drug.patient_instructions || drug.patient_instructions.length === 0) {
        mockAnalyses.push({
          field: 'patient_instructions',
          fieldLabel: 'Instruções ao Paciente',
          currentValue: drug.patient_instructions?.join('\n') || '',
          suggestedValue: `Tome este medicamento conforme orientação médica.\nNão interrompa o tratamento sem consultar seu médico.\nEm caso de efeitos colaterais, procure orientação médica.\nMantenha o medicamento em local seco e protegido da luz.\nNão compartilhe este medicamento com outras pessoas.`,
          reasoning: 'Instruções claras ao paciente são fundamentais para o uso seguro e eficaz do medicamento.',
          confidence: 'high',
          status: 'pending'
        });
      }

      // Analisar classe terapêutica
      if (!drug.therapeutic_classes || drug.therapeutic_classes.length === 0) {
        mockAnalyses.push({
          field: 'therapeutic_classes',
          fieldLabel: 'Classes Terapêuticas',
          currentValue: drug.therapeutic_classes?.join(', ') || '',
          suggestedValue: `[Classes terapêuticas baseadas no princípio ativo ${ingredient.name}]`,
          reasoning: 'As classes terapêuticas são importantes para categorização e busca de medicamentos.',
          confidence: 'medium',
          status: 'pending'
        });
      }

      setAnalyses(mockAnalyses);

      if (mockAnalyses.length === 0) {
        toast({
          title: "Análise Concluída",
          description: "Nenhuma melhoria identificada. Os dados estão em boa qualidade!",
        });
      } else {
        toast({
          title: "Análise Concluída",
          description: `${mockAnalyses.length} sugestões de melhoria identificadas.`,
        });
      }

    } catch (error) {
      console.error('Error analyzing with AI:', error);
      toast({
        title: "Erro",
        description: "Erro ao analisar com IA",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleApprove = async (analysis: AIAnalysis) => {
    try {
      if (!selectedDrug) return;

      const updateData: any = {};
      
      if (analysis.field === 'patient_instructions') {
        updateData[analysis.field] = editingAnalysis === analysis.field 
          ? editedValue.split('\n').filter(line => line.trim() !== '')
          : analysis.suggestedValue.split('\n').filter(line => line.trim() !== '');
      } else {
        updateData[analysis.field] = editingAnalysis === analysis.field 
          ? editedValue 
          : analysis.suggestedValue;
      }

      const { error } = await supabase
        .from('drugs')
        .update(updateData)
        .eq('id', selectedDrug.id);

      if (error) {
        console.error('Error updating drug:', error);
        toast({
          title: "Erro",
          description: "Erro ao atualizar medicamento",
          variant: "destructive"
        });
        return;
      }

      // Atualizar status da análise
      setAnalyses(prev => prev.map(a => 
        a.field === analysis.field 
          ? { ...a, status: 'approved' as const }
          : a
      ));

      setEditingAnalysis(null);
      setEditedValue("");

      toast({
        title: "Sucesso",
        description: "Alteração aprovada e aplicada!",
      });

      onDataUpdated();

    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Erro",
        description: "Erro inesperado ao aplicar alteração",
        variant: "destructive"
      });
    }
  };

  const handleReject = (analysis: AIAnalysis) => {
    setAnalyses(prev => prev.map(a => 
      a.field === analysis.field 
        ? { ...a, status: 'rejected' as const }
        : a
    ));
    
    setEditingAnalysis(null);
    setEditedValue("");

    toast({
      title: "Rejeitado",
      description: "Sugestão rejeitada",
    });
  };

  const startEditing = (analysis: AIAnalysis) => {
    setEditingAnalysis(analysis.field);
    setEditedValue(analysis.suggestedValue);
  };

  const cancelEditing = () => {
    setEditingAnalysis(null);
    setEditedValue("");
  };

  const getConfidenceBadge = (confidence: string) => {
    const config = {
      high: { color: 'bg-green-100 text-green-800', label: 'Alta Confiança' },
      medium: { color: 'bg-yellow-100 text-yellow-800', label: 'Média Confiança' },
      low: { color: 'bg-red-100 text-red-800', label: 'Baixa Confiança' }
    };

    const { color, label } = config[confidence as keyof typeof config];
    return <Badge className={color}>{label}</Badge>;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Seleção de Medicamento para Análise */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BrainCircuit className="h-5 w-5" />
            Análise com IA
          </CardTitle>
          <CardDescription>
            Selecione um medicamento para analisar e melhorar seus dados
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {relatedDrugs.length === 0 ? (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Nenhum medicamento relacionado encontrado para análise.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {relatedDrugs.map((drug) => (
                <Card
                  key={drug.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedDrug?.id === drug.id
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedDrug(drug)}
                >
                  <CardContent className="p-4">
                    <h4 className="font-medium mb-1">{drug.brand_name}</h4>
                    <p className="text-sm text-gray-600">
                      {drug.therapeutic_classes && drug.therapeutic_classes.length > 0
                        ? drug.therapeutic_classes.join(', ')
                        : 'Classes não definidas'}
                    </p>
                    <div className="flex justify-between items-center mt-2">
                      <Badge variant="outline">{drug.status}</Badge>
                      {selectedDrug?.id === drug.id && (
                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            analyzeWithAI(drug);
                          }}
                          disabled={isAnalyzing}
                          className="flex items-center gap-2"
                        >
                          {isAnalyzing ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Sparkles className="h-4 w-4" />
                          )}
                          {isAnalyzing ? 'Analisando...' : 'Analisar com IA'}
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Resultados da Análise */}
      {analyses.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Sugestões de Melhoria ({analyses.length})
            </CardTitle>
            <CardDescription>
              Revise e aprove as sugestões da IA para melhorar os dados
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {analyses.map((analysis, index) => (
              <div key={analysis.field} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(analysis.status)}
                    <h4 className="font-medium">{analysis.fieldLabel}</h4>
                    {getConfidenceBadge(analysis.confidence)}
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Valor Atual:</label>
                    <div className="bg-gray-50 p-3 rounded text-sm">
                      {analysis.currentValue || <em className="text-gray-400">Vazio</em>}
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-600">Sugestão da IA:</label>
                    {editingAnalysis === analysis.field ? (
                      <Textarea
                        value={editedValue}
                        onChange={(e) => setEditedValue(e.target.value)}
                        className="min-h-24"
                        placeholder="Edite a sugestão..."
                      />
                    ) : (
                      <div className="bg-blue-50 p-3 rounded text-sm border-l-4 border-blue-500">
                        {analysis.suggestedValue}
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-600">Justificativa:</label>
                    <p className="text-sm text-gray-700 bg-yellow-50 p-3 rounded">
                      {analysis.reasoning}
                    </p>
                  </div>
                </div>

                {analysis.status === 'pending' && (
                  <div className="flex gap-2 pt-2">
                    {editingAnalysis === analysis.field ? (
                      <>
                        <Button
                          size="sm"
                          onClick={() => handleApprove(analysis)}
                          className="flex items-center gap-2"
                        >
                          <Save className="h-4 w-4" />
                          Salvar e Aprovar
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={cancelEditing}
                          className="flex items-center gap-2"
                        >
                          <RotateCcw className="h-4 w-4" />
                          Cancelar
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          size="sm"
                          onClick={() => handleApprove(analysis)}
                          className="flex items-center gap-2"
                        >
                          <CheckCircle className="h-4 w-4" />
                          Aprovar
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => startEditing(analysis)}
                          className="flex items-center gap-2"
                        >
                          <Edit3 className="h-4 w-4" />
                          Editar
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleReject(analysis)}
                          className="flex items-center gap-2"
                        >
                          <XCircle className="h-4 w-4" />
                          Rejeitar
                        </Button>
                      </>
                    )}
                  </div>
                )}

                {analysis.status === 'approved' && (
                  <Alert className="bg-green-50 border-green-200">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-700">
                      Sugestão aprovada e aplicada com sucesso!
                    </AlertDescription>
                  </Alert>
                )}

                {analysis.status === 'rejected' && (
                  <Alert className="bg-red-50 border-red-200">
                    <XCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-700">
                      Sugestão rejeitada.
                    </AlertDescription>
                  </Alert>
                )}

                {index < analyses.length - 1 && <Separator />}
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
